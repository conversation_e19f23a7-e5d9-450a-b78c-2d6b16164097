<template>
    <div class="bg-white rounded-lg border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200">
    <!-- Mobile: Vertical Layout, Desktop: Horizontal Layout -->
    <div class="flex flex-col md:flex-row">
      <!-- Left: Shot Image and Audio Area -->
      <div class="md:w-80 md:h-44 h-48 bg-gray-50 relative flex-shrink-0">
        <!-- Shot Image -->
        <img
          :src="shot.imageUrl"
          :alt="`镜头 ${index + 1}`"
          class="w-full h-full object-cover"
        />
        
        <!-- Shot Number -->
        <div class="absolute top-3 left-3 w-6 h-6 bg-black bg-opacity-70 text-white rounded-full flex items-center justify-center text-xs font-medium">
          {{ index + 1 }}
        </div>

        <!-- Audio Player Overlay -->
        <div v-if="voiceSettings.isGenerated" class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-3">
          <div class="flex items-center">
            <!-- Waveform -->
            <div class="flex-1 flex items-end h-6 mr-3">
              <div v-for="(height, i) in waveformHeights" :key="i" 
                   :class="['bg-white/70 rounded-full transition-all duration-150 mr-0.5']"
                   :style="{
                     width: '2px',
                     height: `${height}%`,
                     transform: isPlaying ? 'scaleY(1)' : 'scaleY(0.6)',
                     opacity: isPlaying ? 1 : 0.7
                   }">
              </div>
            </div>
            
            <!-- Play Button -->
            <button 
              @click="handlePlayVoice"
              class="w-8 h-8 bg-white text-gray-900 rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors"
            >
              <PlayArrowIcon v-if="!isPlaying" class="text-sm ml-0.5" />
              <PauseIcon v-else class="text-sm" />
            </button>
            
            <!-- Duration -->
            <!-- <span class="text-white text-xs ml-2 font-medium">
              {{ Math.round(voiceSettings.duration || 0) }}s
            </span> -->
          </div>
        </div>

        <!-- Generating Overlay -->
        <div v-else-if="isGenerating" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div class="text-center text-white">
            <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <span class="text-xs">生成中</span>
          </div>
        </div>

        <!-- Voice Status Badge -->
        <div class="absolute top-3 right-3">
          <div v-if="voiceSettings.isGenerated" class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
            已生成
          </div>
          <div v-else-if="isGenerating" class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
            生成中
          </div>
          <div v-else class="bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium">
            待生成
          </div>
        </div>
      </div>

      <!-- Right: Content -->
      <div class="flex-1 p-4 flex flex-col justify-between">
        <!-- Top Section -->
        <div>
          <!-- Script Text -->
          <p class="text-sm text-gray-700 leading-relaxed mb-4 line-clamp-2">
            {{ shot.text }}
          </p>

          <!-- Voice Selection -->
          <div class="mb-4">
            <label class="block text-xs text-gray-500 mb-2">配音类型</label>
            <a-select
              :value="voiceSettings.voiceType || (selectOptions[0] && selectOptions[0].value)"
              @change="handleVoiceTypeSelect"
              :options="selectOptions"
              class="w-full"
              placeholder="选择配音类型"
            >
              <template #option="{ value, label, voice }">
                <div class="flex items-center py-1">
                  <div class="flex items-center w-4/5">
                    <span class="text-base mr-2">{{ voice.icon }}</span>
                    <div class="w-full">
                      <div class="text-sm font-medium">{{ voice.name }}</div>
                      <div class="text-xs text-gray-500">{{ voice.description }}</div>
                    </div>
                  </div>
                  <div class="w-1/5 flex justify-end">
                    <button
                      @click="(e) => { e.stopPropagation(); handlePlayPreview(voice.id); }"
                      class="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors"
                    >
                      {{ currentPlaying === voice.id ? '停止' : '试听' }}
                    </button>
                  </div>
                </div>
              </template>
            </a-select>
          </div>
        </div>

        <!-- Bottom Actions -->
        <div class="flex gap-2">
          <a-button
            type="primary"
            @click="onGenerate"
            :disabled="isGenerating"
            class="flex-1"
          >
            {{ voiceSettings.isGenerated ? '重新生成' : (isGenerating ? '生成中...' : '生成配音') }}
          </a-button>
          <a-button
            @click="onCustomize"
            class="px-3"
            :disabled="isGenerating"
          >
            <TuneIcon />
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { CheckCircleOutlined as CheckCircleIcon, PlayCircleOutlined as PlayArrowIcon, PauseCircleOutlined as PauseIcon, SettingOutlined as TuneIcon, AudioOutlined } from '@ant-design/icons-vue'

interface Shot {
  id: string
  imageUrl: string
  text: string
  audioUrl?: string
}

interface VoiceSettings {
  voiceType?: string
  isGenerated?: boolean
  audioUrl?: string
  duration?: number
  speed?: number
  pitch?: number
  emotion?: string
}

interface Voice {
  id: string
  name: string
  description: string
  icon: string
  previewUrl?: string
  characteristics?: string[]
}

interface Props {
  shot: Shot
  index: number
  voiceSettings: VoiceSettings
  availableVoices: Voice[]
  isGenerating: boolean
  onVoiceSelect: (voiceType: string) => void
  onGenerate: () => void
  onCustomize: () => void
}

const props = defineProps<Props>()

const currentPlaying = ref<string | null>(null)
const isLoadingPreview = ref(false)
const audioRef = ref<HTMLAudioElement | null>(null)
const isPlaying = ref(false)
const voiceAudioRef = ref<HTMLAudioElement | null>(null)
const simulatePlayingTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const previewPlayingTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const waveformHeights = ref<number[]>(Array.from({ length: 20 }, () => Math.random() * 80 + 20))
const waveformTimer = ref<ReturnType<typeof setInterval> | null>(null)

// 组件卸载时清理音频和定时器
onUnmounted(() => {
  if (audioRef.value) {
    audioRef.value.pause()
    audioRef.value.currentTime = 0
  }
  if (voiceAudioRef.value) {
    voiceAudioRef.value.pause()
    voiceAudioRef.value.currentTime = 0
  }
  if (simulatePlayingTimer.value) {
    clearTimeout(simulatePlayingTimer.value)
    simulatePlayingTimer.value = null
  }
  if (previewPlayingTimer.value) {
    clearTimeout(previewPlayingTimer.value)
    previewPlayingTimer.value = null
  }
  if (waveformTimer.value) {
    clearInterval(waveformTimer.value)
    waveformTimer.value = null
  }
})

const handleVoiceTypeSelect = (voiceType: string) => {
  props.onVoiceSelect(voiceType)
}

// 启动波形动画
const startWaveformAnimation = () => {
  if (waveformTimer.value) {
    clearInterval(waveformTimer.value)
  }
  
  waveformTimer.value = setInterval(() => {
    waveformHeights.value = waveformHeights.value.map(() => {
      // 生成更真实的波形变化，模拟音频频谱
      return Math.random() * 70 + 30 // 30-100% 高度范围
    })
  }, 100) // 每100ms更新一次
}

// 停止波形动画
const stopWaveformAnimation = () => {
  if (waveformTimer.value) {
    clearInterval(waveformTimer.value)
    waveformTimer.value = null
  }
  // 恢复到静态状态
  waveformHeights.value = Array.from({ length: 20 }, () => Math.random() * 40 + 20)
}

// 播放配音
const handlePlayVoice = async () => {
  if (isPlaying.value) {
    // 停止播放
    if (voiceAudioRef.value) {
      voiceAudioRef.value.pause()
      voiceAudioRef.value.currentTime = 0
    }
    // 清除模拟播放定时器
    if (simulatePlayingTimer.value) {
      clearTimeout(simulatePlayingTimer.value)
      simulatePlayingTimer.value = null
    }
    isPlaying.value = false
    stopWaveformAnimation()
    return
  }

  try {
    // 播放配音
    const audioUrl = props.voiceSettings.audioUrl
    if (audioUrl) {
      // 停止当前播放的音频
      if (voiceAudioRef.value) {
        voiceAudioRef.value.pause()
        voiceAudioRef.value.currentTime = 0
      }

      // 创建新的音频元素
      const audio = new Audio(audioUrl)
      voiceAudioRef.value = audio
      
      audio.addEventListener('ended', () => {
        isPlaying.value = false
        stopWaveformAnimation()
      })

      audio.addEventListener('error', (e) => {
        console.error('配音播放失败:', e)
        isPlaying.value = false
        stopWaveformAnimation()
      })

      await audio.play()
      isPlaying.value = true
      startWaveformAnimation()
    } else {
      // 模拟播放
      isPlaying.value = true
      startWaveformAnimation()
      // 模拟播放时长
      simulatePlayingTimer.value = setTimeout(() => {
        isPlaying.value = false
        stopWaveformAnimation()
        simulatePlayingTimer.value = null
      }, (props.voiceSettings.duration || 10) * 1000)
    }
  } catch (error) {
    console.error('播放配音失败:', error)
    isPlaying.value = false
    stopWaveformAnimation()
  }
}

// 播放声音预览
const handlePlayPreview = async (voiceId: string) => {
  if (currentPlaying.value === voiceId) {
    // 如果正在播放同一个声音，则停止
    if (audioRef.value) {
      audioRef.value.pause()
      audioRef.value.currentTime = 0
    }
    // 清除预览播放定时器
    if (previewPlayingTimer.value) {
      clearTimeout(previewPlayingTimer.value)
      previewPlayingTimer.value = null
    }
    currentPlaying.value = null
    return
  }

  isLoadingPreview.value = true
  
  try {
    // 从 availableVoices 中查找对应的声音和预览URL
    const voice = props.availableVoices.find(v => v.id === voiceId)
    const previewUrl = voice?.previewUrl

    if (previewUrl) {
      // 停止当前播放的音频
      if (audioRef.value) {
        audioRef.value.pause()
        audioRef.value.currentTime = 0
      }

      // 创建新的音频元素
      const audio = new Audio(previewUrl)
      audioRef.value = audio
      
      audio.addEventListener('ended', () => {
        currentPlaying.value = null
      })

      audio.addEventListener('error', (e) => {
        console.error('音频播放失败:', e)
        currentPlaying.value = null
      })

      await audio.play()
      currentPlaying.value = voiceId
    } else {
      // 模拟试听功能
      currentPlaying.value = voiceId
      // 模拟播放时长
      previewPlayingTimer.value = setTimeout(() => {
        currentPlaying.value = null
        previewPlayingTimer.value = null
      }, 2000)
    }
  } catch (error) {
    console.error('播放预览失败:', error)
  } finally {
    isLoadingPreview.value = false
  }
}

// 为antd Select组件准备选项数据
const selectOptions = computed(() => {
  return props.availableVoices.map(voice => ({
    value: voice.id,
    label: voice.name,
    voice: voice
  }))
})
</script>

<style>
.bg-primary {
  background-color: #1890ff;
}

.text-primary {
  color: #1890ff;
}

.text-primary-dark {
  color: #096dd9;
}

.bg-primary-dark {
  background-color: #096dd9;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 