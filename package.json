{"name": "video-frontend-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@tailwindcss/vite": "^4.1.11", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "pinia": "^3.0.3", "tailwindcss": "^4.1.11", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}