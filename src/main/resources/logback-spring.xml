<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <property name="LOG_PATH" value="./logs"/>

    <!-- console -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSSXXX} %-5p %-18thread TID: %36X{traceId} %-40.40logger{39} : %m%n</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- 按天滚动的文件日志 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/application.log.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSSXXX} %-5p %-18thread TID: %36X{traceId} %-40.40logger{39} : %m%n</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <springProfile name="dev">
        <root level="debug">
            <appender-ref ref="STDOUT" />
        </root>
    </springProfile>

    <springProfile name="prod">  <!-- 或者 name="prod,test" -->
        <root level="info">
            <appender-ref ref="FILE" />
        </root>
    </springProfile>



</configuration>