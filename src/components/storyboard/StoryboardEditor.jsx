import { useState, useEffect, useCallback } from 'react';
import { App } from 'antd';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import { useModal } from '../../hooks/useModal';
import { useApi } from '../../hooks/useApi.js';
import useInterval from '../../hooks/useInterval.js';
import Button from '../common/Button';
import ShotCard from './ShotCard';
import RegenerateModal from './RegenerateModal';
import LoadingSpinner from '../common/LoadingSpinner';

const StoryboardEditor = () => {
  const { state: appState } = useApp();
  const { setCurrentTab } = useNavigation();
  const { getProject, getShots, regenerateShot, triggerShotGeneration } = useApi();
  const { isOpen, modalData, openModal, closeModal } = useModal();
  const { message } = App.useApp();

  const [shots, setShots] = useState([]);
  const [projectStatus, setProjectStatus] = useState('not_started');
  const [isLoading, setIsLoading] = useState(true);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [pollingDelay, setPollingDelay] = useState(null);

  const fetchProjectAndShots = useCallback(async () => {
    if (!appState.projectId) return;

    try {
      const project = await getProject(appState.projectId);
      const status = project.shot_generation_status;
      setProjectStatus(status);

      if (status === 'completed') {
        setPollingDelay(null);
        const shotsData = await getShots(appState.projectId); 
        setShots(shotsData || []);
        setIsLoading(false);
      } else if (status === 'failed') {
        setPollingDelay(null);
        setIsLoading(false);
        message.error('镜头生成失败，请返回上一步重试。');
      } else if (status === 'not_started') {
        setIsLoading(true);
        try {
          await triggerShotGeneration(appState.projectId);
          message.success("已开始为您生成镜头...");
          setPollingDelay(4000); // Start polling
        } catch (err) {
          console.error("Failed to trigger shot generation:", err);
          message.error("无法开始生成镜头，请稍后重试。");
          setPollingDelay(null);
          setIsLoading(false);
        }
      } else { // in_progress
        setPollingDelay(4000); // Start or continue polling
        setIsLoading(true); // Keep loading state while polling
      }
    } catch (error) {
      console.error("Failed to fetch project status or shots:", error);
      setPollingDelay(null);
      setIsLoading(false);
      message.error("无法获取项目状态，请刷新页面。");
    }
  }, [appState.projectId, getProject, getShots, message, triggerShotGeneration]);

  // Initial fetch and polling setup
  useEffect(() => {
    fetchProjectAndShots();
  }, [fetchProjectAndShots]);

  useInterval(fetchProjectAndShots, pollingDelay);

  const handleRegenerateShot = (shot) => {
    openModal(shot);
  };

  const handleConfirmRegenerate = async (shotId, prompt) => {
    if (!shotId) return;
    setIsRegenerating(true);
    closeModal();
    try {
        const updatedShot = await regenerateShot(shotId, { prompt });
        setShots(prev => prev.map(s => s.id === shotId ? updatedShot : s));
        message.success("镜头已重新生成");
    } catch (error) {
        console.error("Failed to regenerate shot:", error);
        message.error("镜头重新生成失败");
    } finally {
        setIsRegenerating(false);
    }
  };

  const handleConfirmShots = () => {
    setCurrentTab('voice');
  };
  
  const renderContent = () => {
    if (isLoading && projectStatus !== 'completed') {
       return <div className="flex justify-center p-8"><LoadingSpinner text="AI镜头生成中，请稍候..." /></div>;
    }
    
    if (projectStatus === 'failed') {
      return <div className="text-center py-12 text-red-500">镜头生成失败，请返回上一步重试。</div>;
    }

    if (shots.length === 0) {
      return <div className="text-center py-12 text-gray-500">未找到任何镜头。</div>
    }

    return (
      <>
        <div className="flex flex-col gap-6 mb-8">
          {shots.map((shot) => (
            <ShotCard
              key={shot.id}
              shot={shot}
              onRegenerate={handleRegenerateShot}
              isProcessing={isRegenerating}
            />
          ))}
        </div>
        <div className="flex justify-end">
          <Button variant="primary" onClick={handleConfirmShots} disabled={shots.length === 0}>
            确认镜头并进入配音
          </Button>
        </div>
      </>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          镜头编辑
        </h2>
        <p className="text-gray-600">
          AI已根据您的脚本生成了以下镜头，您可以对每个镜头的图片进行调整或重新生成
        </p>
      </div>

      {renderContent()}

      <RegenerateModal
        isOpen={isOpen}
        onClose={closeModal}
        shot={modalData}
        onRegenerate={handleConfirmRegenerate}
        isProcessing={isRegenerating}
      />
    </div>
  );
};

export default StoryboardEditor;
