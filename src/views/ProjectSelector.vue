<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Loading State -->
    <div v-if="isLoading" class="min-h-screen flex items-center justify-center bg-gray-50">
      <LoadingSpinner size="large" text="正在初始化应用..." />
    </div>

    <!-- Main Content -->
    <div v-else class="py-12">
      <div class="max-w-4xl mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold text-gray-900 mb-4">
            AI视频生成平台
          </h1>
          <p class="text-xl text-gray-600">
            选择一个项目开始创作，或创建新的视频项目
          </p>
        </div>

        <!-- API连接状态 -->
        <div v-if="!appStore.apiConnected" class="mb-8">
          <div class="max-w-2xl mx-auto">
            <a-alert
              message="API 服务连接失败"
              description="请检查后端服务是否正常运行"
              type="error"
              show-icon
              closable
            />
          </div>
        </div>

        <!-- 创建新项目区域 -->
        <div class="max-w-2xl mx-auto mb-12">
          <div class="bg-white rounded-lg shadow-sm border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
            <div class="p-8 text-center">
              <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <PlusOutlined class="text-2xl text-blue-600" />
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-2">
                创建新项目
              </h3>
              <p class="text-gray-600 mb-6">
                开始一个全新的AI视频生成项目
              </p>
              <Button
                variant="primary"
                size="large"
                :disabled="!appStore.apiConnected || isCreating"
                class="w-full max-w-xs"
                @click="handleCreateNewProject"
              >
                <template v-if="isCreating">
                  <LoadingSpinner size="small" text="" inline class="mr-2" />
                  创建中...
                </template>
                <template v-else>
                  开始创建
                </template>
              </Button>
            </div>
          </div>
        </div>

        <!-- 最近项目区域 -->
        <div class="max-w-4xl mx-auto">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
              <div class="flex items-center">
                <FolderOutlined class="text-2xl text-gray-600 mr-2" />
                <h3 class="text-xl font-semibold text-gray-900 flex" style="margin-bottom: 0px;">
                  最近项目
                </h3>
                <a-tag v-if="projects.length > 0" class="ml-2" color="blue">
                  {{ projects.length }} 个项目
                </a-tag>
              </div>
            </div>

            <div class="p-6">
              <div v-if="projects.length === 0" class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FolderOpenOutlined class="text-2xl text-gray-400" />
                </div>
                <p class="text-gray-500 mb-2">暂无已有项目</p>
                <p class="text-sm text-gray-400">
                  创建您的第一个项目开始使用
                </p>
              </div>

              <div v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
                <div
                  v-for="project in projects"
                  :key="project.id"
                  class="group relative p-4 border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200"
                >
                  <div
                    class="cursor-pointer"
                    @click="handleSelectProject(project.id)"
                  >
                    <div class="flex items-start justify-between mb-2">
                      <h4 class="font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1">
                        {{ project.name }}
                      </h4>
                    </div>
                    <p class="text-sm text-gray-500 mb-3">
                      创建于 {{ formatDate(project.createdAt  ) }}
                    </p>
                    <p v-if="project.theme" class="text-xs text-gray-400 mb-2">
                      主题: {{ project.theme }}
                    </p>
                    <a-tag v-if="project.isDraft" color="gold" size="small">
                      草稿
                    </a-tag>
                  </div>

                  <!-- 删除按钮 -->
                  <a-button
                    type="text"
                    danger
                    size="small"
                    :loading="deletingProjectId === project.id"
                    @click.stop="handleDeleteProject(project.id, project.name)"
                    class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    title="删除项目"
                  >
                    <template #icon>
                      <DeleteOutlined />
                    </template>
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部信息 -->
        <div class="text-center mt-12 text-gray-500">
          <p class="text-sm">
            AI视频生成平台 v1.0.0 | 
            <a-tag 
              :color="appStore.apiConnected ? 'green' : 'red'"
              size="small"
            >
              {{ appStore.apiConnected ? '服务正常' : '服务异常' }}
            </a-tag>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  FolderOutlined, 
  FolderOpenOutlined, 
  DeleteOutlined 
} from '@ant-design/icons-vue'
import { useAppStore } from '../stores/app'
import { useApi } from '../composables/useApi'
import LoadingSpinner from '../components/common/LoadingSpinner.vue'
import Button from '../components/common/Button.vue'

interface Project {
  id: string
  name: string
  theme?: string
  createdAt: string
  isDraft?: boolean
}

const router = useRouter()
const appStore = useAppStore()
const api = useApi()

const projects = ref<Project[]>([])
const isLoading = ref(true)
const isCreating = ref(false)
const deletingProjectId = ref<string | null>(null)

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const initializeApp = async () => {
  try {
    // 检查API连接
    await api.checkApiHealth()

    // 加载已有项目列表
    const projectList = await api.getAllProjects()
    projects.value = projectList
    console.log(projects.value)
  } catch (error) {
    console.error('初始化应用失败:', error)
  } finally {
    isLoading.value = false
  }
}

const handleCreateNewProject = async () => {
  isCreating.value = true
  try {
    // 直接创建一个新项目
    const projectData = {
      name: `新项目 ${new Date().toLocaleString()}`,
      theme: '',
      requirements: '',
      isDraft: true
    }

    const result = await api.createProject(projectData)
    message.success('项目创建成功')

    // 直接跳转到项目的上传内容页面
    router.push(`/project/${result.id}/upload`)
  } catch (error) {
    console.error('创建项目失败:', error)
    message.error('创建项目失败')
  } finally {
    isCreating.value = false
  }
}

const handleSelectProject = (projectId: string) => {
  // 跳转到项目详情页
  router.push(`/project/${projectId}/upload`)
}

const handleDeleteProject = async (projectId: string, projectName: string) => {
  deletingProjectId.value = projectId
  try {
    if (confirm(`确定要删除项目 "${projectName}" 吗？此操作无法撤销。`)) {
      await api.deleteProject(projectId)
      message.success('项目删除成功')

      // 重新加载项目列表
      const updatedProjects = await api.getAllProjects()
      projects.value = updatedProjects
    }
  } catch (error) {
    console.error('删除项目失败:', error)
    message.error('删除项目失败')
  } finally {
    deletingProjectId.value = null
  }
}

onMounted(() => {
  initializeApp()
})
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 