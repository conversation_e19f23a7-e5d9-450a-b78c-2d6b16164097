const { nanoid } = require('nanoid');
const { getDB } = require('../db/database');

// Helper function - should be in a shared file
function rowsFromSql(sqlResult) {
    if (!sqlResult || sqlResult.length === 0) return [];
    const [firstResult] = sqlResult;
    if (!firstResult || !firstResult.columns || !firstResult.values) return [];
    const { columns, values } = firstResult;
    return values.map(row => {
        const rowObject = {};
        columns.forEach((col, i) => { rowObject[col] = row[i]; });
        return rowObject;
    });
}

const generateScripts = (req, res) => {
    const db = getDB();
    const { projectId } = req.body; // Only projectId is needed from the client

    if (!projectId) {
        return res.status(400).json({ message: 'Project ID is required.' });
    }

    // 1. Immediately update status and respond
    try {
        db.prepare(`UPDATE projects SET script_generation_status = 'in_progress' WHERE id = ? and script_generation_status in ('not_started', 'failed')`).run(projectId);
        console.log(`Project ${projectId} script generation status set to 'in_progress'.`);
    } catch (error) {
        console.error('Failed to update project status:', error);
        // This is not critical enough to stop the process, but good to log.
    }

    res.status(202).json({ message: 'Script generation started.', projectId });

    // 2. Perform the actual generation in the background
    setTimeout(() => {
        try {
            console.log(`Starting background script generation for project: ${projectId}`);
            
            // Fetch project data from the database
            const project = db.prepare('SELECT * FROM projects WHERE id = ?').get(projectId);
            if (!project) {
                throw new Error(`Project with ID ${projectId} not found in background task.`);
            }

            // Fetch assets from the database
            const assets = db.prepare('SELECT * FROM assets WHERE project_id = ?').all(projectId);

            // Determine material source based on fetched data
            const hasTextMaterial = project.text_material && project.text_material.trim().length > 0;
            const materialSource = hasTextMaterial ? 'text' : 'upload';

            // This is a simplified simulation of AI script generation.
            const generatedTexts = [
                `根据素材 (${materialSource})：${hasTextMaterial ? project.text_material.substring(0, 20) + '...' : assets.map(f => f.original_name).join(', ')}，我们构思了第一个场景。`,
                "第二个场景将深入探讨核心价值，展现产品的独特魅力。",
                "高潮部分，用户的使用体验和成功案例将集中展示。",
                "最后，以一个强有力的号召性用语结束，鼓励观众采取行动。",
                "这是一个由AI生成的结尾彩蛋脚本。"
            ];

            const insertStmt = db.prepare(
                `INSERT INTO scripts (id, project_id, text, order_index, created_at, updated_at)
                 VALUES (@id, @project_id, @text, @order_index, @created_at, @updated_at)`
            );

            const now = new Date().toISOString();
            db.transaction(() => {
                db.prepare('DELETE FROM scripts WHERE project_id = ?').run(projectId);
                generatedTexts.forEach((text, index) => {
                    insertStmt.run({
                        id: `script_${nanoid()}`,
                        project_id: projectId,
                        text: text,
                        order_index: index,
                        created_at: now,
                        updated_at: now,
                    });
                });
            })();
            
            db.prepare(`UPDATE projects SET script_generation_status = 'completed' WHERE id = ?`).run(projectId);
            console.log(`✅ Project ${projectId} script generation status set to 'completed'.`);

        } catch (error) {
            console.error(`Background script generation failed for project ${projectId}:`, error);
            db.prepare(`UPDATE projects SET script_generation_status = 'failed' WHERE id = ?`).run(projectId);
        }
    }, 2000); // 2-second delay to simulate async work
};

const getProjectScripts = (req, res) => {
    const db = getDB();
    const { projectId } = req.params;
    try {
        const scripts = db.prepare('SELECT * FROM scripts WHERE project_id = ? ORDER BY order_index ASC').all(projectId);
        res.json({ success: true, scripts });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Failed to get scripts' });
    }
};

const updateScript = (req, res) => {
  const db = getDB();
  const { scriptId } = req.params;
  const { text, order_index } = req.body;
  
  const updates = [];
  const params = { id: scriptId, updated_at: new Date().toISOString() };

  if (text !== undefined) {
      updates.push("text = @text");
      params.text = text;
  }
  if (order_index !== undefined) {
      updates.push("order_index = @order_index");
      params.order_index = order_index;
  }
  
  if (updates.length === 0) {
      return res.status(400).json({ error: "No fields to update" });
  }

  try {
    const stmt = `UPDATE scripts SET ${updates.join(', ')}, updated_at = @updated_at WHERE id = @id`;
    const info = db.prepare(stmt).run(params);

    if (info.changes === 0) return res.status(404).json({ message: 'Script not found' });
    
    const updatedScript = db.prepare('SELECT * FROM scripts WHERE id = ?').get(scriptId);
    res.status(200).json(updatedScript);
  } catch (error) {
    console.error('Failed to update script:', error);
    res.status(500).json({ error: 'Failed to update script' });
  }
};

const deleteScript = (req, res) => {
  const db = getDB();
  const { scriptId } = req.params;
  try {
    const info = db.prepare('DELETE FROM scripts WHERE id = ?').run(scriptId);
    if (info.changes === 0) return res.status(404).json({ message: 'Script not found' });
    res.status(200).json({ message: 'Script deleted successfully' });
  } catch (error) {
    console.error('Failed to delete script:', error);
    res.status(500).json({ error: 'Failed to delete script' });
  }
};

const triggerShotGeneration = (req, res) => {
    const db = getDB();
    const { projectId } = req.params;

    if (!projectId) {
        return res.status(400).json({ message: 'Project ID is required.' });
    }

    // 1. Immediately update status and respond
    try {
        const info = db.prepare(`UPDATE projects SET shot_generation_status = 'in_progress' WHERE id = ? AND shot_generation_status in ('not_started', 'failed')`).run(projectId);
        if (info.changes === 0) {
            // This could mean the project doesn't exist or generation is already in progress.
            // For the client, either way, we can just let them know it's started.
        }
    } catch (error) {
        console.error('Failed to update project shot_generation_status:', error);
    }
    
    res.status(202).json({ message: 'Shot generation started.' });

    // 2. Perform the actual generation in the background
    setTimeout(() => {
        try {
            console.log(`Starting background shot generation for project: ${projectId}`);
            
            const scripts = db.prepare('SELECT * FROM scripts WHERE project_id = ? ORDER BY order_index ASC').all(projectId);
            if (!scripts || scripts.length === 0) {
                throw new Error('No scripts found for this project.');
            }

            const insertStmt = db.prepare(
                `INSERT INTO shots (id, project_id, script_id, shot_number, script_text, image_url, created_at, updated_at)
                 VALUES (@id, @project_id, @script_id, @shot_number, @script_text, @image_url, @created_at, @updated_at)`
            );
            
            const now = new Date().toISOString();
            const shotsToSave = scripts.map((script, index) => ({
                id: `shot_${nanoid()}`,
                project_id: projectId,
                script_id: script.id,
                shot_number: index + 1,
                script_text: script.text,
                image_url: `https://picsum.photos/seed/${nanoid(5)}/400/225`,
                created_at: now,
                updated_at: now,
            }));
            
            db.transaction(() => {
                db.prepare('DELETE FROM shots WHERE project_id = ?').run(projectId);
                for (const shot of shotsToSave) insertStmt.run(shot);
            })();

            db.prepare(`UPDATE projects SET shot_generation_status = 'completed' WHERE id = ?`).run(projectId);
            console.log(`✅ Project ${projectId} shot generation status set to 'completed'.`);

        } catch (error) {
            console.error(`Background shot generation failed for project ${projectId}:`, error);
            db.prepare(`UPDATE projects SET shot_generation_status = 'failed' WHERE id = ?`).run(projectId);
        }
    }, 5000); // 5-second delay to simulate image generation
};

// Mock data generator
function generateMockScripts(projectName, materialSource, uploadedFiles, textMaterial) {
    if (materialSource === 'text' && textMaterial) {
        return [
            { id: nanoid(), text: `基于文本"${textMaterial.substring(0, 10)}..."的脚本1` },
            { id: nanoid(), text: `基于文本"${textMaterial.substring(0, 10)}..."的脚本2` },
        ];
    }
    return [
        { id: nanoid(), text: `关于 ${projectName} 的默认脚本1` },
        { id: nanoid(), text: `关于 ${projectName} 的默认脚本2` },
    ];
}

const getScriptsByProjectId = (req, res) => {
    const db = getDB();
    const { projectId } = req.params;
    try {
        const scripts = db.prepare('SELECT * FROM scripts WHERE project_id = ? ORDER BY order_index').all(projectId);
        res.status(200).json(scripts || []);
    } catch (error) {
        console.error(`Failed to get scripts for project ${projectId}:`, error);
        res.status(500).json({ error: 'Failed to retrieve scripts' });
    }
};

const addScript = (req, res) => {
    const db = getDB();
    const { projectId, text, order_index } = req.body;
    const now = new Date().toISOString();
    const newScript = {
        id: `script_${nanoid()}`,
        project_id: projectId,
        text,
        order_index,
        created_at: now,
        updated_at: now,
    };
    try {
        db.prepare(
            `INSERT INTO scripts (id, project_id, text, order_index, created_at, updated_at)
             VALUES (@id, @project_id, @text, @order_index, @created_at, @updated_at)`
        ).run(newScript);
        res.status(201).json(newScript);
    } catch (error) {
        console.error('Failed to add script:', error);
        res.status(500).json({ error: 'Failed to add script' });
    }
};

const regenerateScript = (req, res) => {
    const db = getDB();
    const { scriptId } = req.params;
    const newText = "这是一条由后端AI重新生成的、全新的、充满创意的脚本内容。";
    try {
        const info = db.prepare('UPDATE scripts SET text = ?, updated_at = ? WHERE id = ?')
                       .run(newText, new Date().toISOString(), scriptId);
        if (info.changes === 0) return res.status(404).json({ message: 'Script not found' });
        
        const updatedScript = db.prepare('SELECT * FROM scripts WHERE id = ?').get(scriptId);
        res.status(200).json(updatedScript);
    } catch (error) {
        console.error('Failed to regenerate script:', error);
        res.status(500).json({ error: 'Failed to regenerate script' });
    }
};

const getShotsByProjectId = (req, res) => {
    const db = getDB();
    const { projectId } = req.params;
    try {
        const shots = db.prepare('SELECT * FROM shots WHERE project_id = ? ORDER BY shot_number ASC').all(projectId);
        res.status(200).json(shots || []);
    } catch (error) {
        console.error(`Failed to get shots for project ${projectId}:`, error);
        res.status(500).json({ error: 'Failed to retrieve shots' });
    }
};

const regenerateShot = (req, res) => {
    const db = getDB();
    const { shotId } = req.params;
    const { prompt } = req.body;
    
    if (prompt) {
        console.log(`Regenerating shot ${shotId} with custom prompt: "${prompt}"`);
    } else {
        console.log(`Regenerating shot ${shotId} without a prompt.`);
    }

    // Simulate AI regeneration by creating a new image URL
    const newImageUrl = `https://picsum.photos/seed/${nanoid(5)}/400/225`;
    const updatedAt = new Date().toISOString();

    try {
        const info = db.prepare('UPDATE shots SET image_url = ?, updated_at = ? WHERE id = ?')
                       .run(newImageUrl, updatedAt, shotId);

        if (info.changes === 0) {
            return res.status(404).json({ message: 'Shot not found' });
        }
        
        const updatedShot = db.prepare('SELECT * FROM shots WHERE id = ?').get(shotId);
        res.status(200).json(updatedShot);
    } catch (error) {
        console.error(`Failed to regenerate shot ${shotId}:`, error);
        res.status(500).json({ error: 'Failed to regenerate shot' });
    }
};

module.exports = {
  generateScripts,
  getProjectScripts,
  updateScript,
  deleteScript,
  triggerShotGeneration,
  getScriptsByProjectId,
  addScript,
  regenerateScript,
  getShotsByProjectId,
  regenerateShot,
};
