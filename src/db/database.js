const path = require('path');
const Database = require('better-sqlite3');

const dbPath = path.resolve(process.env.DATABASE_PATH || 'database.sqlite');
let db;

function initializeDatabase() {
    try {
        db = new Database(dbPath, { verbose: console.log });
        console.log(`📖 数据库已连接: ${db.name}`);

        // 优化：在单个事务中创建所有表
        const createSchema = db.transaction(() => {
            db.exec(`
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    theme TEXT,
                    requirements TEXT,
                    text_material TEXT,
                    script_generation_status TEXT DEFAULT 'not_started',
                    shot_generation_status TEXT DEFAULT 'not_started',
                    is_draft INTEGER DEFAULT 0,
                    avatar_id TEXT,
                    voice_id TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                );
            `);
            db.exec(`
                CREATE TABLE IF NOT EXISTS assets (
                    id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    original_name TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    mimetype TEXT,
                    size INTEGER,
                    url TEXT,
                    uploaded_at TEXT NOT NULL
                );
            `);
            db.exec(`
                CREATE TABLE IF NOT EXISTS scripts (
                    id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    text TEXT,
                    order_index INTEGER,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                );
            `);
            db.exec(`
                CREATE TABLE IF NOT EXISTS shots (
                    id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    script_id TEXT,
                    shot_number INTEGER,
                    script_text TEXT,
                    image_url TEXT,
                    video_url TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                );
            `);
            db.exec(`
                CREATE TABLE IF NOT EXISTS tasks (
                    id TEXT PRIMARY KEY,
                    project_id TEXT,
                    type TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'pending',
                    result TEXT,
                    error TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                );
            `);
        });
        
        createSchema();
        console.log('🏛️  所有表已创建或确认存在');

        // 应用退出时安全关闭数据库
        process.on('exit', () => {
            if (db && db.open) {
                db.close();
                console.log('数据库连接已关闭');
            }
        });
        process.on('SIGINT', () => process.exit());


    } catch (err) {
        console.error('❌ 数据库初始化失败:', err);
        throw err;
    }
}

function getDB() {
  if (!db) {
    throw new Error('数据库未初始化！');
  }
  return db;
}

module.exports = {
  initializeDatabase,
  getDB,
}; 