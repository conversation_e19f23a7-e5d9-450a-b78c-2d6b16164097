const express = require('express');
const router = express.Router();
const projectController = require('../controllers/projectController');
const upload = require('../middleware/upload');

// POST /api/projects - Create a new project
router.post('/', projectController.createProject);

// GET /api/projects - Get all projects
router.get('/', projectController.getAllProjects);

// GET /api/projects/:projectId - Get a project by ID
router.get('/:projectId', projectController.getProjectById);

// PUT /api/projects/:projectId - Update a project
router.put('/:projectId', projectController.updateProject);

// DELETE /api/projects/:projectId - Delete a project
router.delete('/:projectId', projectController.deleteProject);

// POST /api/projects/:projectId/assets - Upload an asset
router.post('/:projectId/assets', upload.single('asset'), projectController.uploadAsset);

// GET /api/projects/:projectId/assets - Get project assets
router.get('/:projectId/assets', projectController.getProjectAssets);

// DELETE /api/projects/:projectId/assets/:assetId - Delete an asset
router.delete('/:projectId/assets/:assetId', projectController.deleteAsset);

module.exports = router;
