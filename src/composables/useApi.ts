import axios from 'axios'
import { useAppStore } from '../stores/app'
import { message } from 'ant-design-vue'

// 配置 axios 默认设置，超时设置为10分钟
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 600000,
})

// 请求拦截器
api.interceptors.request.use((config) => {
  const store = useAppStore()
  store.setLoading(true)
  return config
})

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const store = useAppStore()
    store.setLoading(false)
    if (!response.data.success) {
      message.error(response.data.message)
    }
    return response.data
  },
  (error) => {
    const store = useAppStore()
    store.setLoading(false)
    store.setError(error.message)
    throw error
  }
)

export const useApi = () => {
  const store = useAppStore()

  const checkApiHealth = async () => {
    const response = await api.get('/')
    store.setApiConnected(true)
    return response.data
  }

  const getAllProjects = async () => {
    const response = await api.get('/api/projects')
    return response.data
  }

  const getProject = async (projectId: string) => {
    const response = await api.get(`/api/projects/${projectId}`)
    return response.data
  }

  const createProject = async (projectData: any) => {
    const response = await api.post('/api/projects', projectData)
    return response.data
  }

  const updateProject = async (projectId: string, projectData: any) => {
    const response = await api.put(`/api/projects/${projectId}`, projectData)
    return response.data
  }

  const deleteProject = async (projectId: string) => {
    const response = await api.delete(`/api/projects/${projectId}`)
    return response.data
  }

  const uploadFile = async (projectId: string, file: File, onProgress?: (progress: number) => void) => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await api.post(`/api/projects/${projectId}/assets/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total && onProgress) {
            const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
            onProgress(progress)
          }
        },
      })
    return response.data
  }

  const generateScript = async (projectId: string, content: string) => {
    const response = await api.post('/api/scripts/generate', {
      projectId,
      content
    })
    return response.data
  }

  const generateScripts = async (data: { projectId: string }) => {
    const response = await api.post(`/api/scripts/generate/${data.projectId}`)
    return response.data
  }

  const getScript = async (projectId: string) => {
    const response = await api.get(`/api/scripts/${projectId}`)
    return response.data
  }

  const getScripts = async (projectId: string) => {
    const response = await api.get(`/api/scripts/project/${projectId}`)
    return response.data
  }

  const addScript = async (scriptData: any) => {
    const response = await api.post('/api/scripts', scriptData)
    return response.data
  }

  const insertScript = async (projectId: string, text: string, insertIndex?: number) => {
    const response = await api.post('/api/scripts/insert', {
      projectId,
      text,
      insertIndex
    })
    return response.data
  }

  const reorderScripts = async (projectId: string, scriptIds: string[]) => {
    const response = await api.post('/api/scripts/reorder', {
      projectId,
      scriptIds
    })
    return response.data
  }

  const deleteScript = async (scriptId: string) => {
    const response = await api.delete(`/api/scripts/${scriptId}`)
    return response.data
  }

  const updateScript = async (scriptId: string, content: string) => {
    const response = await api.put(`/api/scripts/${scriptId}`, {
      text: content
    })
    return response.data
  }

  const regenerateScript = async (scriptId: string) => {
    const response = await api.post(`/api/scripts/${scriptId}/regenerate`)
    return response.data
  }

  const generateShots = async (projectId: string) => {
    const response = await api.post(`/api/scripts/project/${projectId}/generate-shots`)
    return response.data
  }

  const getTask = async (taskId: string) => {
    const response = await api.get(`/api/tasks/${taskId}`)
    return response.data
  }

  const cancelTask = async (taskId: string) => {
    const response = await api.delete(`/api/tasks/${taskId}`)
    return response.data
  }

  const getConfig = async () => {
    const response = await api.get('/api/config')
    return response.data
  }

  const getShots = async (projectId: string) => {
    const response = await api.get(`/api/scripts/project/${projectId}/shots`)
    return response.data
  }

  const regenerateShot = async (shotId: string, data: { prompt: string }) => {
    const response = await api.post(`/api/scripts/shots/${shotId}/regenerate`, data)
    return response.data
  }

  const triggerShotGeneration = async (projectId: string) => {
    const response = await api.post(`/api/scripts/project/${projectId}/generate-shots`)
    return response.data
  }

  const getVoices = async () => {
    const response = await api.get('/api/resources/voices')
    return response.data
  }

  const generateVoice = async (voiceData: any) => {
    const response = await api.post(`/api/scripts/generate-voice`, voiceData)
    return response.data
  }

  const generateBatchVoice = async (batchData: any) => {
    const response = await api.post('/api/voices/generate-batch', batchData)
    return response.data.data
  }

  const generateVideo = async (videoData: any) => {
    const response = await api.post('/api/scripts/generate-video', videoData)
    return response.data
  }

  const deleteAsset = async (assetId: string) => {
    const response = await api.delete(`/api/projects/assets/${assetId}`)
    return response.data
  }

  const getProjectAssets = async (projectId: string) => {
    const response = await api.get(`/api/projects/${projectId}/assets`)
    return response.data
  }

  const uploadShotImage = async (shotId: string, file: File) => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await api.post(`/api/scripts/shots/${shotId}/upload-image`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  }

  const generateAvatarVideo = async (shotId: string, avatarId: string) => {
    const response = await api.post(`/api/scripts/shots/${shotId}/generate-avatar`, { avatarId })
    return response.data
  }

  const getAvatars = async () => {
    const response = await api.get('/api/resources/avatars')
    return response.data
  }

  const clearAvatar = async (shotId: string) => {
    const response = await api.post(`/api/scripts/shots/${shotId}/clear-avatar`)
    return response.data
  }

  const composeVideo = async (projectId: string) => {
    const response = await api.post(`/api/projects/${projectId}/compose`)
    return response.data
  }

  return {
    checkApiHealth,
    getAllProjects,
    getProject,
    createProject,
    updateProject,
    deleteProject,
    uploadFile,
    generateScript,
    generateScripts,
    getScript,
    getScripts,
    addScript,
    insertScript,
    reorderScripts,
    deleteScript,
    updateScript,
    regenerateScript,
    generateShots,
    getShots,
    regenerateShot,
    triggerShotGeneration,
    getVoices,
    generateVoice,
    generateBatchVoice,
    getTask,
    cancelTask,
    getConfig,
    generateVideo,
    deleteAsset,
    getProjectAssets,
    uploadShotImage,
    generateAvatarVideo,
    getAvatars,
    clearAvatar,
    composeVideo,
  }
} 