import logging
import os
import shutil
import subprocess
import threading
import uuid
from datetime import datetime

import cv2
import numpy as np
import torch
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename

import audio
import face_detection
from models import Wav2Lip

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求


# 配置
class Config:
    # 文件路径配置
    CHECKPOINT_PATH = "checkpoints/wav2lip_gan.pth"
    UPLOAD_FOLDER = "uploads"
    OUTPUT_FOLDER = "outputs"
    TEMP_FOLDER = "temp"

    # 允许的文件扩展名
    ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
    ALLOWED_AUDIO_EXTENSIONS = {'wav', 'mp3', 'aac', 'm4a', 'flac'}

    # 文件大小限制 (MB)
    MAX_IMAGE_SIZE = 50
    MAX_AUDIO_SIZE = 100

    # Wav2Lip 参数
    DEFAULT_FPS = 25.0
    DEFAULT_PADS = [0, 10, 0, 0]
    FACE_DET_BATCH_SIZE = 16
    WAV2LIP_BATCH_SIZE = 128
    IMG_SIZE = 96

    # 设备配置
    DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'


config = Config()

# 全局变量
model = None
task_status = {}  # 存储任务状态

# 确保必要目录存在
for folder in [config.UPLOAD_FOLDER, config.OUTPUT_FOLDER, config.TEMP_FOLDER]:
    os.makedirs(folder, exist_ok=True)


class Wav2LipProcessor:
    """Wav2Lip 处理器类，封装所有处理逻辑"""

    def __init__(self, checkpoint_path=None):
        self.checkpoint_path = checkpoint_path or config.CHECKPOINT_PATH
        self.model = None
        self.device = config.DEVICE
        logger.info(f'Using {self.device} for inference.')

    def load_model(self):
        """加载Wav2Lip模型"""
        if self.model is None:
            try:
                self.model = Wav2Lip()
                logger.info(f"Loading checkpoint from: {self.checkpoint_path}")

                if self.device == 'cuda':
                    checkpoint = torch.load(self.checkpoint_path)
                else:
                    checkpoint = torch.load(self.checkpoint_path, map_location='cpu')

                # 处理模型权重
                state_dict = checkpoint["state_dict"]
                new_state_dict = {}
                for k, v in state_dict.items():
                    new_state_dict[k.replace('module.', '')] = v

                self.model.load_state_dict(new_state_dict)
                self.model = self.model.to(self.device)
                self.model.eval()

                logger.info("Model loaded successfully!")
                return True
            except Exception as e:
                logger.error(f"Failed to load model: {str(e)}")
                return False
        return True

    def get_smoothened_boxes(self, boxes, T=5):
        """平滑检测框"""
        for i in range(len(boxes)):
            if i + T > len(boxes):
                window = boxes[len(boxes) - T:]
            else:
                window = boxes[i: i + T]
            boxes[i] = np.mean(window, axis=0)
        return boxes

    def face_detect(self, images, batch_size=None):
        """人脸检测"""
        if batch_size is None:
            batch_size = config.FACE_DET_BATCH_SIZE

        detector = face_detection.FaceAlignment(
            face_detection.LandmarksType._2D,
            flip_input=False,
            device=self.device
        )

        while True:
            predictions = []
            try:
                for i in range(0, len(images), batch_size):
                    batch = np.array(images[i:i + batch_size])
                    predictions.extend(detector.get_detections_for_batch(batch))
                break
            except RuntimeError as e:
                if batch_size == 1:
                    raise RuntimeError('Image too big for face detection. Please use smaller image.')
                batch_size //= 2
                logger.warning(f'OOM error, reducing batch size to: {batch_size}')
                continue

        results = []
        pady1, pady2, padx1, padx2 = config.DEFAULT_PADS

        for rect, image in zip(predictions, images):
            if rect is None:
                # 使用默认框或跳过
                h, w = image.shape[:2]
                x1, y1, x2, y2 = w // 4, h // 4, 3 * w // 4, 3 * h // 4
                logger.warning('Face not detected, using default box')
            else:
                y1 = max(0, rect[1] - pady1)
                y2 = min(image.shape[0], rect[3] + pady2)
                x1 = max(0, rect[0] - padx1)
                x2 = min(image.shape[1], rect[2] + padx2)

            results.append([x1, y1, x2, y2])

        boxes = np.array(results)
        boxes = self.get_smoothened_boxes(boxes, T=5)

        face_results = []
        for image, (x1, y1, x2, y2) in zip(images, boxes):
            face_crop = image[y1:y2, x1:x2]
            face_results.append([face_crop, (y1, y2, x1, x2)])

        del detector
        return face_results

    def process_frames_and_audio(self, face_path, audio_path, output_path,
                                 fps=None, is_static=True, task_id=None):
        """处理图片/视频和音频，生成唇同步视频"""
        try:
            # 更新任务状态
            if task_id:
                task_status[task_id]['status'] = 'processing'
                task_status[task_id]['progress'] = 10

            fps = fps or config.DEFAULT_FPS

            # 加载人脸图像/视频
            if face_path.lower().endswith(('.jpg', '.jpeg', '.png')):
                full_frames = [cv2.imread(face_path)]
                is_static = True
            else:
                video_stream = cv2.VideoCapture(face_path)
                fps = video_stream.get(cv2.CAP_PROP_FPS) or fps

                full_frames = []
                while True:
                    ret, frame = video_stream.read()
                    if not ret:
                        break
                    full_frames.append(frame)
                video_stream.release()
                is_static = False

            if not full_frames:
                raise ValueError("No frames found in input")

            logger.info(f"Loaded {len(full_frames)} frames")

            # 更新进度
            if task_id:
                task_status[task_id]['progress'] = 30

            # 处理音频
            if not audio_path.endswith('.wav'):
                temp_wav = os.path.join(config.TEMP_FOLDER, f"{task_id}_temp.wav")
                cmd = f'ffmpeg -y -i "{audio_path}" -ar 16000 "{temp_wav}"'
                result = subprocess.run(cmd, shell=True, capture_output=True)
                if result.returncode != 0:
                    raise RuntimeError(f"Audio conversion failed: {result.stderr.decode()}")
                audio_path = temp_wav

            # 加载音频并转换为梅尔频谱
            wav = audio.load_wav(audio_path, 16000)
            mel = audio.melspectrogram(wav)

            if np.isnan(mel.reshape(-1)).sum() > 0:
                raise ValueError('Audio contains invalid values')

            # 分割梅尔频谱
            mel_step_size = 16
            mel_idx_multiplier = 80.0 / fps
            mel_chunks = []

            i = 0
            while True:
                start_idx = int(i * mel_idx_multiplier)
                if start_idx + mel_step_size > len(mel[0]):
                    mel_chunks.append(mel[:, len(mel[0]) - mel_step_size:])
                    break
                mel_chunks.append(mel[:, start_idx:start_idx + mel_step_size])
                i += 1

            logger.info(f"Generated {len(mel_chunks)} mel chunks")

            # 匹配帧数和音频长度
            full_frames = full_frames[:len(mel_chunks)]

            # 更新进度
            if task_id:
                task_status[task_id]['progress'] = 50

            # 人脸检测
            if is_static:
                face_det_results = self.face_detect([full_frames[0]])
            else:
                face_det_results = self.face_detect(full_frames)

            # 生成视频
            self._generate_video(full_frames, mel_chunks, face_det_results,
                                 output_path, fps, is_static, task_id)

            # 合并音频
            final_output = output_path.replace('.avi', '_final.mp4')
            cmd = f'ffmpeg -y -i "{output_path}" -i "{audio_path}" -c:v libx264 -c:a aac -strict experimental "{final_output}"'
            result = subprocess.run(cmd, shell=True, capture_output=True)

            if result.returncode == 0 and os.path.exists(final_output):
                # 替换原文件
                shutil.move(final_output, output_path.replace('.avi', '.mp4'))
                output_path = output_path.replace('.avi', '.mp4')

            # 更新任务状态
            if task_id:
                task_status[task_id]['status'] = 'completed'
                task_status[task_id]['progress'] = 100
                task_status[task_id]['output_path'] = output_path

            return output_path

        except Exception as e:
            if task_id:
                task_status[task_id]['status'] = 'failed'
                task_status[task_id]['error'] = str(e)
            logger.error(f"Processing failed: {str(e)}")
            raise

    def _generate_video(self, frames, mel_chunks, face_det_results,
                        output_path, fps, is_static, task_id):
        """生成视频的核心逻辑"""
        batch_size = config.WAV2LIP_BATCH_SIZE
        img_size = config.IMG_SIZE

        # 创建视频写入器
        frame_h, frame_w = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'XVID')
        out = cv2.VideoWriter(output_path, fourcc, fps, (frame_w, frame_h))

        total_batches = int(np.ceil(len(mel_chunks) / batch_size))
        processed_batches = 0

        # 批处理生成
        for batch_start in range(0, len(mel_chunks), batch_size):
            batch_end = min(batch_start + batch_size, len(mel_chunks))
            batch_mels = mel_chunks[batch_start:batch_end]

            img_batch, mel_batch, frame_batch, coords_batch = [], [], [], []

            for i, mel in enumerate(batch_mels):
                frame_idx = 0 if is_static else (batch_start + i) % len(frames)
                frame = frames[frame_idx].copy()
                face, coords = face_det_results[frame_idx if not is_static else 0]

                # 调整人脸大小
                face_resized = cv2.resize(face, (img_size, img_size))

                img_batch.append(face_resized)
                mel_batch.append(mel)
                frame_batch.append(frame)
                coords_batch.append(coords)

            # 准备模型输入
            img_batch = np.asarray(img_batch)
            mel_batch = np.asarray(mel_batch)

            # 创建遮罩
            img_masked = img_batch.copy()
            img_masked[:, img_size // 2:] = 0

            # 归一化和格式化
            img_input = np.concatenate((img_masked, img_batch), axis=3) / 255.0
            mel_input = np.reshape(mel_batch, [len(mel_batch), mel_batch.shape[1], mel_batch.shape[2], 1])

            # 转换为张量
            img_tensor = torch.FloatTensor(np.transpose(img_input, (0, 3, 1, 2))).to(self.device)
            mel_tensor = torch.FloatTensor(np.transpose(mel_input, (0, 3, 1, 2))).to(self.device)

            # 模型推理
            with torch.no_grad():
                pred = self.model(mel_tensor, img_tensor)

            # 后处理预测结果
            pred = pred.cpu().numpy().transpose(0, 2, 3, 1) * 255.0

            # 将结果写入视频
            for prediction, frame, coords in zip(pred, frame_batch, coords_batch):
                y1, y2, x1, x2 = coords
                # 调整预测结果大小
                pred_resized = cv2.resize(prediction.astype(np.uint8), (x2 - x1, y2 - y1))
                # 替换原帧中的人脸区域
                frame[y1:y2, x1:x2] = pred_resized
                out.write(frame)

            processed_batches += 1

            # 更新进度
            if task_id:
                progress = 50 + int((processed_batches / total_batches) * 40)
                task_status[task_id]['progress'] = min(progress, 90)

        out.release()


# 全局处理器实例
processor = Wav2LipProcessor()


def allowed_file(filename, allowed_extensions):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in allowed_extensions


def validate_file_size(file, max_size_mb):
    """验证文件大小"""
    file.seek(0, 2)  # 移到文件末尾
    size = file.tell()
    file.seek(0)  # 重置到开头
    return size <= max_size_mb * 1024 * 1024


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': processor.model is not None,
        'device': config.DEVICE,
        'timestamp': datetime.now().isoformat()
    })


@app.route('/upload', methods=['POST'])
def upload_files():
    """文件上传接口"""
    try:
        # 检查文件是否存在
        if 'face' not in request.files or 'audio' not in request.files:
            return jsonify({'error': 'Missing face or audio file'}), 400

        face_file = request.files['face']
        audio_file = request.files['audio']

        # 验证文件名
        if face_file.filename == '' or audio_file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # 验证文件类型
        if not allowed_file(face_file.filename, config.ALLOWED_IMAGE_EXTENSIONS):
            return jsonify({'error': 'Invalid face image format'}), 400

        if not allowed_file(audio_file.filename, config.ALLOWED_AUDIO_EXTENSIONS):
            return jsonify({'error': 'Invalid audio format'}), 400

        # 验证文件大小
        if not validate_file_size(face_file, config.MAX_IMAGE_SIZE):
            return jsonify({'error': f'Face image too large (max {config.MAX_IMAGE_SIZE}MB)'}), 400

        if not validate_file_size(audio_file, config.MAX_AUDIO_SIZE):
            return jsonify({'error': f'Audio file too large (max {config.MAX_AUDIO_SIZE}MB)'}), 400

        # 生成唯一任务ID
        task_id = str(uuid.uuid4())

        # 保存文件
        face_filename = secure_filename(f"{task_id}_face_{face_file.filename}")
        audio_filename = secure_filename(f"{task_id}_audio_{audio_file.filename}")

        face_path = os.path.join(config.UPLOAD_FOLDER, face_filename)
        audio_path = os.path.join(config.UPLOAD_FOLDER, audio_filename)

        face_file.save(face_path)
        audio_file.save(audio_path)

        # 初始化任务状态
        task_status[task_id] = {
            'status': 'uploaded',
            'progress': 0,
            'face_path': face_path,
            'audio_path': audio_path,
            'created_at': datetime.now().isoformat()
        }

        return jsonify({
            'task_id': task_id,
            'message': 'Files uploaded successfully'
        })

    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/synthesize/<task_id>', methods=['POST'])
def synthesize_video(task_id):
    """开始视频合成"""
    try:
        # 检查任务是否存在
        if task_id not in task_status:
            return jsonify({'error': 'Task not found'}), 404

        if task_status[task_id]['status'] not in ['uploaded', 'failed']:
            return jsonify({'error': 'Task already in progress or completed'}), 400

        # 确保模型已加载
        if not processor.load_model():
            return jsonify({'error': 'Failed to load model'}), 500

        # 获取参数
        params = request.get_json() or {}
        fps = params.get('fps', config.DEFAULT_FPS)

        # 生成输出路径
        output_filename = f"{task_id}_output.mp4"
        output_path = os.path.join(config.OUTPUT_FOLDER, output_filename)

        # 在后台线程中处理
        def process_task():
            try:
                processor.process_frames_and_audio(
                    face_path=task_status[task_id]['face_path'],
                    audio_path=task_status[task_id]['audio_path'],
                    output_path=output_path,
                    fps=fps,
                    task_id=task_id
                )
            except Exception as e:
                logger.error(f"Background processing error: {str(e)}")
                task_status[task_id]['status'] = 'failed'
                task_status[task_id]['error'] = str(e)

        # 启动后台任务
        thread = threading.Thread(target=process_task)
        thread.daemon = True
        thread.start()

        return jsonify({
            'task_id': task_id,
            'message': 'Synthesis started',
            'status': 'processing'
        })

    except Exception as e:
        logger.error(f"Synthesis error: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取任务状态"""
    if task_id not in task_status:
        return jsonify({'error': 'Task not found'}), 404

    status_data = task_status[task_id].copy()
    # 移除敏感信息
    status_data.pop('face_path', None)
    status_data.pop('audio_path', None)

    return jsonify(status_data)


@app.route('/download/<task_id>', methods=['GET'])
def download_result(task_id):
    """下载结果视频"""
    if task_id not in task_status:
        return jsonify({'error': 'Task not found'}), 404

    if task_status[task_id]['status'] != 'completed':
        return jsonify({'error': 'Task not completed'}), 400

    output_path = task_status[task_id].get('output_path')
    if not output_path or not os.path.exists(output_path):
        return jsonify({'error': 'Output file not found'}), 404

    return send_file(output_path, as_attachment=True,
                     download_name=f"wav2lip_result_{task_id}.mp4")


@app.route('/cleanup/<task_id>', methods=['DELETE'])
def cleanup_task(task_id):
    """清理任务文件"""
    if task_id not in task_status:
        return jsonify({'error': 'Task not found'}), 404

    try:
        status = task_status[task_id]

        # 删除文件
        for path_key in ['face_path', 'audio_path', 'output_path']:
            path = status.get(path_key)
            if path and os.path.exists(path):
                os.remove(path)

        # 删除任务状态
        del task_status[task_id]

        return jsonify({'message': 'Task cleaned up successfully'})

    except Exception as e:
        logger.error(f"Cleanup error: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/tasks', methods=['GET'])
def list_tasks():
    """列出所有任务"""
    tasks = {}
    for task_id, status in task_status.items():
        safe_status = status.copy()
        safe_status.pop('face_path', None)
        safe_status.pop('audio_path', None)
        tasks[task_id] = safe_status

    return jsonify(tasks)


if __name__ == '__main__':
    # 预加载模型
    logger.info("Loading Wav2Lip model...")
    if processor.load_model():
        logger.info("Model loaded successfully!")
    else:
        logger.error("Failed to load model!")

    # 启动服务器
    app.run(host='0.0.0.0', port=1206, debug=False, threaded=True)
