package com.example.controller;

import com.example.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 健康检查控制器
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@RestController
@Tag(name = "系统信息", description = "系统健康检查和配置信息")
public class HealthController {

    @Value("${spring.application.name:video-backend-java}")
    private String applicationName;

    @Value("${server.port:9800}")
    private String serverPort;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    @Value("${app.api-base-url:http://localhost:9800}")
    private String apiBaseUrl;

    @Value("${app.upload-path:./uploads}")
    private String uploadPath;

    @Value("${app.static-path:./static}")
    private String staticPath;

    @Operation(summary = "健康检查", description = "获取系统运行状态")
    @GetMapping("/")
    public Result<Map<String, Object>> health() {
        Map<String, Object> status = Map.of(
            "message", "Video Backend API",
            "version", "1.0.0",
            "environment", activeProfile,
            "status", "running"
        );
        return Result.success(status);
    }

    @Operation(summary = "配置信息", description = "获取系统配置信息")
    @GetMapping("/config")
    public Result<Map<String, Object>> config() {
        Map<String, Object> config = Map.of(
            "applicationName", applicationName,
            "port", serverPort,
            "environment", activeProfile,
            "apiBaseUrl", apiBaseUrl,
            "uploadPath", uploadPath,
            "staticPath", staticPath,
            "staticUrl", "/static"
        );
        return Result.success(config);
    }
} 