package com.example.param.task;

import com.example.entity.Task;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 任务创建参数
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Data
public class TaskCreateParam {

    private String projectId;

    @NotNull(message = "任务类型不能为空")
    private Task.TaskType taskType;

    /**
     * 任务数据（JSON格式）
     */
    private String taskData;
} 