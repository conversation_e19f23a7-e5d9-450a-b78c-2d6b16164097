package com.example.config.web;


import com.example.utils.TraceIdGenerator;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(Integer.MIN_VALUE)
public class TraceIdFilter implements Filter {

    private static final String TRACE_ID = "traceId";
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        String traceId = TraceIdGenerator.getTraceId();
        MDC.put(TRACE_ID, traceId);
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
        httpResponse.setHeader(TRACE_ID, traceId);
        try {
            // 继续请求处理
            filterChain.doFilter(servletRequest, servletResponse);
        } finally {
            // 清除 traceId
            MDC.remove(TRACE_ID);
            TraceIdGenerator.clearTraceId();
        }
    }
}
