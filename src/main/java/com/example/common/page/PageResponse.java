package com.example.common.page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.function.Function;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageResponse<T> {

    private List<T> list;

    private long total;

    public static <T, R> PageResponse<R> of(Page<T> page, Function<T, R> function) {
        return new PageResponse<>(page.getContent().stream().map(function).toList(), page.getTotalElements());
    }

    public static <T, R> PageResponse<R> of(List<T> list, long total, Function<T, R> function) {
        return new PageResponse<>(list.stream().map(function).toList(), total);
    }

}
