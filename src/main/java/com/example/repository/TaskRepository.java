package com.example.repository;

import com.example.entity.Task;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 任务Repository接口
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Repository
public interface TaskRepository extends JpaRepository<Task, String>, JpaSpecificationExecutor<Task> {

    /**
     * 根据项目ID查询任务，按创建时间降序
     */
    List<Task> findByProjectIdOrderByCreatedAtDesc(String projectId);

    /**
     * 根据任务状态查询任务
     */
    List<Task> findByStatusOrderByCreatedAtDesc(Task.TaskStatus status);

    /**
     * 根据任务类型查询任务
     */
    List<Task> findByTypeOrderByCreatedAtDesc(Task.TaskType type);

    /**
     * 根据项目ID和任务类型查询任务
     */
    List<Task> findByProjectIdAndTypeOrderByCreatedAtDesc(String projectId, Task.TaskType type);
} 