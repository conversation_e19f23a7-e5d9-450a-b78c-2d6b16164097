<template>
  <div>
    <!-- Header -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold text-gray-900 mb-2">
        创建新项目
      </h2>
      <p class="text-gray-600">
        填写项目信息并上传相关素材，AI将为您生成精彩视频
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center p-8">
      <LoadingSpinner text="正在加载项目数据..." />
    </div>

    <!-- Form Container -->
    <div v-else class="max-w-4xl bg-white rounded-lg p-8 shadow-sm">
      <!-- Project Name -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          项目名称
        </label>
        <input
          v-model="projectData.name"
          type="text"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="给项目起个名字"
        />
      </div>

      <!-- Project Theme -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          视频主题
        </label>
        <input
          v-model="projectData.theme"
          type="text"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="例如：产品介绍、企业宣传、教育培训等"
        />
      </div>

      <!-- Project Requirements -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          详细需求
        </label>
        <textarea
          v-model="projectData.requirements"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="描述您对视频的具体要求，包括目标受众、核心信息、时长等"
        ></textarea>
      </div>

      <!-- Text Material -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          文本素材
        </label>
        <textarea
          v-model="projectData.textMaterial"
          rows="8"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="请在此处粘贴或输入您的文本素材，例如文章、大纲、核心要点等..."
        ></textarea>
      </div>

      <!-- File Upload -->
      <FileUpload 
        @update:files="setUploadedFiles" 
      />

      <!-- Uploaded Files List -->
      <div v-if="projectAssets.length > 0" class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          已上传文件
        </label>
        <div class="border border-gray-200 rounded-md">
          <div
            v-for="asset in projectAssets"
            :key="asset.id"
            class="flex items-center justify-between p-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50"
          >
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900">{{ asset.originalName }}</div>
                <div class="text-xs text-gray-500">{{ formatFileSize(asset.size) }}</div>
              </div>
            </div>
            <a-popconfirm title="确定删除该素材吗？" ok-text="确定" cancel-text="取消" @confirm="handleDeleteAsset(asset.id)">
              <svg class="w-4 h-4 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </a-popconfirm>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end gap-4">
        <Button
          variant="secondary"
          @click="handleSaveProject"
          :disabled="isGenerating"
        >
          保存项目
        </Button>
        <Button
          variant="primary"
          @click="handleStartGeneration"
          :disabled="isGenerating"
          :loading="isGenerating"
        >
          {{ isGenerating ? '正在生成...' : '开始生成' }}
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useNavigation } from '../../composables/useNavigation'
import { useApi } from '../../composables/useApi'
import Button from '../common/Button.vue'
import LoadingSpinner from '../common/LoadingSpinner.vue'
import FileUpload from './FileUpload.vue'
import { message } from 'ant-design-vue'

const route = useRoute()
const { setCurrentTab } = useNavigation()
const api = useApi()

const isGenerating = ref(false)
const isLoading = ref(true)
const projectData = ref({
  name: '未命名项目',
  theme: '',
  requirements: '',
  textMaterial: '',
})
const projectAssets = ref<any[]>([])

const setUploadedFiles = () => {
  api.getProjectAssets(route.params.projectId as string).then((res) => {
    projectAssets.value = res
  })
}

const formatFileSize = (bytes: string | number) => {
  const size = typeof bytes === 'string' ? parseInt(bytes) : bytes
  if (size === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const loadProjectData = async () => {
  const projectId = route.params.projectId as string
  if (projectId) {
    isLoading.value = true
    try {
      const project = await api.getProject(projectId)
      if (project) {
        projectData.value = {
          name: project.name,
          theme: project.theme || '',
          requirements: project.requirements || '',
          textMaterial: project.textMaterial || '',
        }

        // 加载项目资源
        if (project.assets && project.assets.length > 0) {
          projectAssets.value = project.assets
        }
      }
    } catch (error) {
      console.error('加载项目数据失败:', error)
      message.error('加载项目数据失败，请刷新页面重试')
    } finally {
      isLoading.value = false
    }
  } else {
    isLoading.value = false
  }
}

const handleSaveProject = async () => {
  const projectId = route.params.projectId as string
  try {
    await api.updateProject(projectId, {
      name: projectData.value.name,
      theme: projectData.value.theme,
      requirements: projectData.value.requirements,
      textMaterial: projectData.value.textMaterial,
    })
    message.success('项目保存成功')
  } catch (error) {
    console.error('保存项目失败:', error)
    message.error('保存项目失败，请重试')
  }
}

const handleDeleteAsset = async (assetId: string) => {
  try {    

    // 这里需要添加删除资源的API调用
    await api.deleteAsset(assetId)
    
    // 从本地状态中移除
    projectAssets.value = projectAssets.value.filter(asset => asset.id !== assetId)
    message.success('删除素材成功，项目已自动保存')
  } catch (error) {
    console.error('删除素材失败:', error)
    message.error('删除素材失败，请重试')
  }
}

const handleStartGeneration = async () => {
  if (projectAssets.value.length === 0 && !projectData.value.textMaterial.trim()) {
    message.warning('请上传素材文件或输入文本素材')
    return
  }

  const projectId = route.params.projectId as string
  isGenerating.value = true
  try {
    // Always update the project with the latest data before generating scripts
    await api.updateProject(projectId, {
      name: projectData.value.name,
      theme: projectData.value.theme,
      requirements: projectData.value.requirements,
      textMaterial: projectData.value.textMaterial,
    })

    // Fire and forget - don't wait for the script generation to complete
    api.generateScripts({ projectId }).catch(err => {
      // Even if it fails, we navigate. The user will see the status on the next page.
      console.error("Initiating script generation failed:", err)
    })

    // Immediately navigate to the script page
    setCurrentTab('script')
    
  } catch (error) {
    console.error('生成脚本失败:', error)
    message.error('生成脚本失败，请重试')
  } finally {
    isGenerating.value = false
  }
}

onMounted(() => {
  loadProjectData()
})
</script>