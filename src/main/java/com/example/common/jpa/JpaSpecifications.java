package com.example.common.jpa;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.From;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;

/**
 * Merged JPA Specifications library - all classes in one file
 */
public class JpaSpecifications {

    // ==================== LambdaUtils ====================

    private static final Map<String, String> FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * Extract field name from a serializable function (lambda expression)
     */
    public static <T, R> String getFieldName(SerializableFunction<T, R> getter) {
        try {
            String cacheKey = getter.getClass().getName() + "#" + extractMethodName(getter);
            return FIELD_CACHE.computeIfAbsent(cacheKey, key -> {
                try {
                    String methodName = extractMethodName(getter);
                    if (methodName.startsWith("get")) {
                        return decapitalize(methodName.substring(3));
                    } else if (methodName.startsWith("is")) {
                        return decapitalize(methodName.substring(2));
                    }
                    return methodName;
                } catch (Exception e) {
                    throw new RuntimeException("Failed to extract field name from lambda expression", e);
                }
            });
        } catch (Exception e) {
            throw new RuntimeException("Failed to extract field name", e);
        }
    }

    /**
     * Extract method name from a serializable function
     */
    public static <T, R> String extractMethodName(SerializableFunction<T, R> lambda) throws Exception {
        Method writeReplace = lambda.getClass().getDeclaredMethod("writeReplace");
        writeReplace.setAccessible(true);
        SerializedLambda serializedLambda = (SerializedLambda) writeReplace.invoke(lambda);
        return serializedLambda.getImplMethodName();
    }

    /**
     * Decapitalize the first character of a string
     */
    private static String decapitalize(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return Character.toLowerCase(name.charAt(0)) + name.substring(1);
    }

    /**
     * Functional interface for serializable functions (lambda expressions)
     */
    @FunctionalInterface
    public interface SerializableFunction<T, R> extends Function<T, R>, Serializable {
    }

    // ==================== Specifications Factory ====================

    public static <T> PredicateBuilder<T> and() {
        return new PredicateBuilder<>(Predicate.BooleanOperator.AND);
    }

    public static <T> PredicateBuilder<T> or() {
        return new PredicateBuilder<>(Predicate.BooleanOperator.OR);
    }

    // ==================== Sorts Builder ====================

    public static SortBuilder sortBuilder() {
        return new SortBuilder();
    }

    public static final class SortBuilder {
        private final List<Order> orders;

        public SortBuilder() {
            this.orders = new ArrayList<>();
        }

        public SortBuilder asc(String property) {
            return asc(true, property);
        }

        public SortBuilder desc(String property) {
            return desc(true, property);
        }

        public SortBuilder asc(boolean condition, String property) {
            if (condition) {
                orders.add(new Order(ASC, property));
            }
            return this;
        }

        public SortBuilder desc(boolean condition, String property) {
            if (condition) {
                orders.add(new Order(DESC, property));
            }
            return this;
        }

        public Sort build() {
            return Sort.by(orders);
        }
    }

    // ==================== Abstract Specification Base ====================

    abstract static class AbstractSpecification<T> implements Specification<T>, Serializable {
        public String getProperty(String property) {
            if (property.contains(".")) {
                return StringUtils.split(property, ".")[1];
            }
            return property;
        }

        public From<?, ?> getRoot(String property, Root<T> root) {
            if (property.contains(".")) {
                String joinProperty = StringUtils.split(property, ".")[0];
                return root.join(joinProperty, JoinType.LEFT);
            }
            return root;
        }
    }

    // ==================== Equal Specification ====================

    public static class EqualSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Object[] values;

        public EqualSpecification(String property, Object... values) {
            this.property = property;
            this.values = values;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            if (values == null) {
                return cb.isNull(from.get(field));
            }
            if (values.length == 1) {
                return getPredicate(from, cb, values[0], field);
            }

            Predicate[] predicates = new Predicate[values.length];
            for (int i = 0; i < values.length; i++) {
                predicates[i] = getPredicate(from, cb, values[i], field);
            }
            return cb.or(predicates);
        }

        private Predicate getPredicate(From<?, ?> root, CriteriaBuilder cb, Object value, String field) {
            Object val = normalizeValue(value);
            return val == null ? cb.isNull(root.get(field)) : cb.equal(root.get(field), val);
        }

        private Object normalizeValue(Object value) {
            return value;
        }
    }

    // ==================== Not Equal Specification ====================

    public static class NotEqualSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Object[] values;

        public NotEqualSpecification(String property, Object... values) {
            this.property = property;
            this.values = values;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            if (values == null) {
                return cb.isNotNull(from.get(field));
            }
            if (values.length == 1) {
                return getPredicate(from, cb, values[0], field);
            }
            Predicate[] predicates = new Predicate[values.length];
            for (int i = 0; i < values.length; i++) {
                predicates[i] = getPredicate(from, cb, values[i], field);
            }
            return cb.or(predicates);
        }

        private Predicate getPredicate(From<?, ?> root, CriteriaBuilder cb, Object value, String field) {
            Object val = normalizeValue(value);
            return val == null ? cb.isNotNull(root.get(field)) : cb.notEqual(root.get(field), val);
        }

        private Object normalizeValue(Object value) {
            return value;
        }
    }

    // ==================== Like Specification ====================

    public static class LikeSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final String[] patterns;

        public LikeSpecification(String property, String... patterns) {
            this.property = property;
            this.patterns = patterns;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            if (patterns.length == 1) {
                return cb.like(from.get(field), patterns[0]);
            }
            Predicate[] predicates = new Predicate[patterns.length];
            for (int i = 0; i < patterns.length; i++) {
                predicates[i] = cb.like(from.get(field), patterns[i]);
            }
            return cb.or(predicates);
        }
    }

    // ==================== Not Like Specification ====================

    public static class NotLikeSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final String[] patterns;

        public NotLikeSpecification(String property, String... patterns) {
            this.property = property;
            this.patterns = patterns;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            if (patterns.length == 1) {
                return cb.like(from.get(field), patterns[0]).not();
            }
            Predicate[] predicates = new Predicate[patterns.length];
            for (int i = 0; i < patterns.length; i++) {
                predicates[i] = cb.like(from.get(field), patterns[i]).not();
            }
            return cb.or(predicates);
        }
    }

    // ==================== Between Specification ====================

    public static class BetweenSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Comparable<Object> lower;
        private final transient Comparable<Object> upper;

        public BetweenSpecification(String property, Object lower, Object upper) {
            this.property = property;
            this.lower = (Comparable<Object>) lower;
            this.upper = (Comparable<Object>) upper;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            return cb.between(from.get(field), lower, upper);
        }
    }

    // ==================== In Specification ====================

    public static class InSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Collection<?> values;

        public InSpecification(String property, Collection<?> values) {
            this.property = property;
            this.values = values;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            return from.get(field).in(values);
        }
    }

    // ==================== Not In Specification ====================

    public static class NotInSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Collection<?> values;

        public NotInSpecification(String property, Collection<?> values) {
            this.property = property;
            this.values = values;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            return from.get(field).in(values).not();
        }
    }

    // ==================== Greater Than Specification ====================

    public static class GtSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Comparable<Object> compare;

        public GtSpecification(String property, Comparable<? extends Object> compare) {
            this.property = property;
            this.compare = (Comparable<Object>) compare;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            return cb.greaterThan(from.get(field), compare);
        }
    }

    // ==================== Greater Than or Equal Specification ====================

    public static class GeSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Comparable<Object> compare;

        public GeSpecification(String property, Comparable<?> compare) {
            this.property = property;
            this.compare = (Comparable<Object>) compare;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            return cb.greaterThanOrEqualTo(from.get(field), compare);
        }
    }

    // ==================== Less Than Specification ====================

    public static class LtSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Comparable<Object> compare;

        public LtSpecification(String property, Comparable<? extends Object> compare) {
            this.property = property;
            this.compare = (Comparable<Object>) compare;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            return cb.lessThan(from.get(field), compare);
        }
    }

    // ==================== Less Than or Equal Specification ====================

    public static class LeSpecification<T> extends AbstractSpecification<T> {
        private final String property;
        private final transient Comparable<Object> compare;

        public LeSpecification(String property, Comparable<? extends Object> compare) {
            this.property = property;
            this.compare = (Comparable<Object>) compare;
        }

        @Override
        public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
            From<?, ?> from = getRoot(property, root);
            String field = getProperty(property);
            return cb.lessThanOrEqualTo(from.get(field), compare);
        }
    }

    // ==================== PredicateBuilder ====================

    public static class PredicateBuilder<T> {

        private final Predicate.BooleanOperator operator;
        private final List<Specification<T>> specifications;

        public PredicateBuilder(Predicate.BooleanOperator operator) {
            this.operator = operator;
            this.specifications = new ArrayList<>();
        }

        public PredicateBuilder<T> eq(String property, Object... values) {
            return eq(true, property, values);
        }

        public PredicateBuilder<T> eq(boolean condition, String property, Object... values) {
            return this.predicate(condition, new EqualSpecification<T>(property, values));
        }

        public PredicateBuilder<T> ne(String property, Object... values) {
            return ne(true, property, values);
        }

        public PredicateBuilder<T> ne(boolean condition, String property, Object... values) {
            return this.predicate(condition, new NotEqualSpecification<T>(property, values));
        }

        public PredicateBuilder<T> gt(String property, Comparable<?> compare) {
            return gt(true, property, compare);
        }

        public PredicateBuilder<T> gt(boolean condition, String property, Comparable<?> compare) {
            return this.predicate(condition, new GtSpecification<T>(property, compare));
        }

        public PredicateBuilder<T> ge(String property, Comparable<?> compare) {
            return ge(true, property, compare);
        }

        public PredicateBuilder<T> ge(boolean condition, String property, Comparable<?> compare) {
            return this.predicate(condition, new GeSpecification<T>(property, compare));
        }

        public PredicateBuilder<T> lt(String property, Comparable<?> compare) {
            return lt(true, property, compare);
        }

        public PredicateBuilder<T> lt(boolean condition, String property, Comparable<?> compare) {
            return this.predicate(condition, new LtSpecification<T>(property, compare));
        }

        public PredicateBuilder<T> le(String property, Comparable<?> compare) {
            return le(true, property, compare);
        }

        public PredicateBuilder<T> le(boolean condition, String property, Comparable<?> compare) {
            return this.predicate(condition, new LeSpecification<T>(property, compare));
        }

        public PredicateBuilder<T> between(String property, Object lower, Object upper) {
            return between(true, property, lower, upper);
        }

        public PredicateBuilder<T> between(boolean condition, String property, Object lower, Object upper) {
            return this.predicate(condition, new BetweenSpecification<T>(property, lower, upper));
        }

        public PredicateBuilder<T> like(String property, String... patterns) {
            return like(true, property, patterns);
        }

        public PredicateBuilder<T> like(boolean condition, String property, String... patterns) {
            return this.predicate(condition, new LikeSpecification<T>(property, patterns));
        }

        public PredicateBuilder<T> notLike(String property, String... patterns) {
            return notLike(true, property, patterns);
        }

        public PredicateBuilder<T> notLike(boolean condition, String property, String... patterns) {
            return this.predicate(condition, new NotLikeSpecification<T>(property, patterns));
        }

        public PredicateBuilder<T> in(String property, Collection<?> values) {
            return in(true, property, values);
        }

        public PredicateBuilder<T> in(boolean condition, String property, Collection<?> values) {
            return this.predicate(condition, new InSpecification<T>(property, values));
        }

        public PredicateBuilder<T> notIn(String property, Collection<?> values) {
            return notIn(true, property, values);
        }

        public PredicateBuilder<T> notIn(boolean condition, String property, Collection<?> values) {
            return this.predicate(condition, new NotInSpecification<T>(property, values));
        }

        public PredicateBuilder<T> predicate(Specification<T> specification) {
            return predicate(true, specification);
        }

        public PredicateBuilder<T> predicate(boolean condition, Specification<T> specification) {
            if (condition) {
                this.specifications.add(specification);
            }
            return this;
        }

        // Lambda-based methods
        public <R> PredicateBuilder<T> eq(SerializableFunction<T, R> getter, Object... values) {
            return eq(true, getter, values);
        }

        public <R> PredicateBuilder<T> eq(boolean condition, SerializableFunction<T, R> getter, Object... values) {
            String property = getFieldName(getter);
            return this.predicate(condition, new EqualSpecification<T>(property, values));
        }

        public <R> PredicateBuilder<T> ne(SerializableFunction<T, R> getter, Object... values) {
            return ne(true, getter, values);
        }

        public <R> PredicateBuilder<T> ne(boolean condition, SerializableFunction<T, R> getter, Object... values) {
            String property = getFieldName(getter);
            return this.predicate(condition, new NotEqualSpecification<T>(property, values));
        }

        public <R extends Comparable<R>> PredicateBuilder<T> gt(SerializableFunction<T, R> getter, R compare) {
            return gt(true, getter, compare);
        }

        public <R extends Comparable<R>> PredicateBuilder<T> gt(boolean condition, SerializableFunction<T, R> getter, R compare) {
            String property = getFieldName(getter);
            return this.predicate(condition, new GtSpecification<T>(property, compare));
        }

        public <R extends Comparable<R>> PredicateBuilder<T> ge(SerializableFunction<T, R> getter, R compare) {
            return ge(true, getter, compare);
        }

        public <R extends Comparable<R>> PredicateBuilder<T> ge(boolean condition, SerializableFunction<T, R> getter, R compare) {
            String property = getFieldName(getter);
            return this.predicate(condition, new GeSpecification<T>(property, compare));
        }

        public <R extends Comparable<R>> PredicateBuilder<T> lt(SerializableFunction<T, R> getter, R compare) {
            return lt(true, getter, compare);
        }

        public <R extends Comparable<R>> PredicateBuilder<T> lt(boolean condition, SerializableFunction<T, R> getter, R compare) {
            String property = getFieldName(getter);
            return this.predicate(condition, new LtSpecification<T>(property, compare));
        }

        public <R extends Comparable<R>> PredicateBuilder<T> le(SerializableFunction<T, R> getter, R compare) {
            return le(true, getter, compare);
        }

        public <R extends Comparable<R>> PredicateBuilder<T> le(boolean condition, SerializableFunction<T, R> getter, R compare) {
            String property = getFieldName(getter);
            return this.predicate(condition, new LeSpecification<T>(property, compare));
        }

        public <R> PredicateBuilder<T> between(SerializableFunction<T, R> getter, R lower, R upper) {
            return between(true, getter, lower, upper);
        }

        public <R> PredicateBuilder<T> between(boolean condition, SerializableFunction<T, R> getter, R lower, R upper) {
            String property = getFieldName(getter);
            return this.predicate(condition, new BetweenSpecification<T>(property, lower, upper));
        }

        public PredicateBuilder<T> like(SerializableFunction<T, String> getter, String... patterns) {
            return like(true, getter, patterns);
        }

        public PredicateBuilder<T> like(boolean condition, SerializableFunction<T, String> getter, String... patterns) {
            String property = getFieldName(getter);
            return this.predicate(condition, new LikeSpecification<T>(property, patterns));
        }

        public PredicateBuilder<T> notLike(SerializableFunction<T, String> getter, String... patterns) {
            return notLike(true, getter, patterns);
        }

        public PredicateBuilder<T> notLike(boolean condition, SerializableFunction<T, String> getter, String... patterns) {
            String property = getFieldName(getter);
            return this.predicate(condition, new NotLikeSpecification<T>(property, patterns));
        }

        public <R> PredicateBuilder<T> in(SerializableFunction<T, R> getter, Collection<R> values) {
            return in(true, getter, values);
        }

        public <R> PredicateBuilder<T> in(boolean condition, SerializableFunction<T, R> getter, Collection<R> values) {
            String property = getFieldName(getter);
            return this.predicate(condition, new InSpecification<T>(property, values));
        }

        public <R> PredicateBuilder<T> notIn(SerializableFunction<T, R> getter, Collection<R> values) {
            return notIn(true, getter, values);
        }

        public <R> PredicateBuilder<T> notIn(boolean condition, SerializableFunction<T, R> getter, Collection<R> values) {
            String property = getFieldName(getter);
            return this.predicate(condition, new NotInSpecification<T>(property, values));
        }

        public Specification<T> build() {
            return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
                Predicate[] predicates = new Predicate[specifications.size()];
                for (int i = 0; i < specifications.size(); i++) {
                    predicates[i] = specifications.get(i).toPredicate(root, query, cb);
                }
                if (Objects.equals(predicates.length, 0)) {
                    return null;
                }
                return Predicate.BooleanOperator.OR.equals(operator) ? cb.or(predicates) : cb.and(predicates);
            };
        }
    }
}