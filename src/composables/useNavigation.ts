import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '../stores/app'

export interface Tab {
  id: string
  label: string
  icon: string
}

export const useNavigation = () => {
  const route = useRoute()
  const router = useRouter()
  const appStore = useAppStore()

  const tabs: Tab[] = [
    { id: 'upload', label: '上传内容', icon: 'cloud_upload' },
    { id: 'script', label: '生成脚本', icon: 'description' },
    { id: 'storyboard', label: '镜头编辑', icon: 'photo_library' },
    { id: 'voice', label: '生成配音', icon: 'record_voice_over' },
    { id: 'video', label: '生成视频', icon: 'videocam' },
    { id: 'avatar', label: '添加数字人', icon: 'person' },
    { id: 'compose', label: '视频合成', icon: 'video_library' },
    { id: 'export', label: '导出视频', icon: 'movie' }
  ]

  const currentTab = computed(() => appStore.currentTab)

  const setCurrentTab = (tabId: string) => {
    appStore.setCurrentTab(tabId)

    // 导航到对应的路由（如果在项目页面中且当前路由不匹配）
    if (route.params.projectId) {
      const currentTabFromRoute = route.meta?.tab as string
      if (currentTabFromRoute !== tabId) {
        const routeName = getRouteNameFromTabId(tabId)
        router.push({ 
          name: routeName, 
          params: { projectId: route.params.projectId }
        })
      }
    }

    // Auto-generate shots when entering storyboard tab
    if (tabId === 'storyboard' && appStore.shots.length === 0) {
      appStore.generateShots()
    }
  }

  // 根据tab ID获取对应的路由名称
  const getRouteNameFromTabId = (tabId: string): string => {
    const routeNameMap: Record<string, string> = {
      'upload': 'Upload',
      'script': 'Script', 
      'storyboard': 'Storyboard',
      'voice': 'Voice',
      'video': 'Video',
      'avatar': 'Avatar',
      'compose': 'Compose',
      'export': 'Export'
    }
    return routeNameMap[tabId] || 'Upload'
  }

  const getCurrentTabIndex = () => {
    return tabs.findIndex(tab => tab.id === currentTab.value)
  }

  const getCompletedSteps = () => {
    const currentIndex = getCurrentTabIndex()
    return tabs.slice(0, currentIndex + 1).map(tab => tab.id)
  }

  const canNavigateToTab = (tabId: string) => {
    const tabIndex = tabs.findIndex(tab => tab.id === tabId)
    const currentIndex = getCurrentTabIndex()
    
    // Allow navigation to current tab and previous tabs
    // Allow navigation to next tab only if current requirements are met
    if (tabIndex <= currentIndex) return true
    if (tabIndex === currentIndex + 1) {
      // Add specific validation logic here if needed
      return true
    }
    return false
  }

  return {
    tabs,
    currentTab,
    setCurrentTab,
    getCurrentTabIndex,
    getCompletedSteps,
    canNavigateToTab
  }
} 