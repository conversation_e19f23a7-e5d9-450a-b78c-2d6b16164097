<template>
  <div>
    <!-- Header -->
    <div class="mb-6">
      <h2 class="text-xl font-medium text-gray-900 mb-1">
        AI脚本生成
      </h2>
      <p class="text-sm text-gray-500">
        AI已为您生成以下脚本，您可以对每一条进行编辑或重新生成
      </p>
    </div>

    <!-- Content -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <LoadingSpinner text="正在加载项目数据..." />
    </div>

    <div v-else-if="projectStatus === 'in_progress' || projectStatus === 'not_started'" class="flex justify-center py-8">
      <LoadingSpinner text="AI脚本生成中，请稍候..." />
    </div>

    <div v-else-if="projectStatus === 'failed'" class="text-center py-8 text-red-500 text-sm">
      脚本生成失败，请返回上一步重试。
    </div>

    <template v-else>
      <div class="mb-6">
        <ScriptList 
          v-model:scripts="scripts" 
          @refresh-scripts="handleRefreshScripts"
          @insert-script="handleInsertScript"
          @reorder-scripts="handleReorderScripts"
        />
      </div>
      <div class="flex justify-between items-center pt-4 border-t border-gray-100">
        <a-button size="small" @click="handleAddScript">
          <template #icon>
            <PlusOutlined />
          </template>
          添加新条目
        </a-button>
        <a-button 
          type="primary" 
          size="small"
          @click="handleConfirmScript" 
          :disabled="scripts.length === 0"
        >
          进入镜头编辑
        </a-button>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useAppStore } from '../../stores/app'
import { useNavigation } from '../../composables/useNavigation'
import { useApi } from '../../composables/useApi'
import { useInterval } from '../../composables/useInterval'
import LoadingSpinner from '../common/LoadingSpinner.vue'
import ScriptList from './ScriptList.vue'

interface Script {
  id: string
  text: string
  isNew?: boolean
  order_index: number
}

const appStore = useAppStore()
const { setCurrentTab } = useNavigation()
const { getProject, getScripts, insertScript, reorderScripts } = useApi()

const scripts = ref<Script[]>([])
const projectStatus = ref<string>('not_started')
const isLoading = ref(true)
const pollingDelay = ref<number | null>(null)

const fetchProjectStatus = async () => {
  if (!appStore.projectId) return

  try {
    const project = await getProject(appStore.projectId)
    const status = project.scriptGenerationStatus || 'completed' // Default to completed for now
    projectStatus.value = status

    if (status === 'completed') {
      pollingDelay.value = null // Stop polling
      const scriptsData = await getScripts(appStore.projectId)
      scripts.value = scriptsData
      isLoading.value = false
    } else if (status === 'failed') {
      pollingDelay.value = null
      isLoading.value = false
      message.error('脚本生成失败，请返回上一步重试。')
    } else { // in_progress or not_started
      pollingDelay.value = 3000 // Start or continue polling
      isLoading.value = false // We are not 'page loading' anymore, but 'generating'
    }
  } catch (error) {
    console.error("Failed to fetch project status:", error)
    pollingDelay.value = null
    isLoading.value = false
    message.error("无法获取项目状态，请刷新页面。")
  }
}

// Polling interval using useInterval
useInterval(fetchProjectStatus, pollingDelay)

const handleAddScript = () => {
  const newOrderIndex = scripts.value.length > 0 
    ? Math.max(...scripts.value.map(s => s.order_index)) + 1 
    : 0

  const newScript: Script = {
    id: `local_${Date.now()}`,
    text: '',
    isNew: true,
    order_index: newOrderIndex,
  }
  scripts.value = [...scripts.value, newScript]
}

const handleInsertScript = (insertIndex: number) => {
  const newScript: Script = {
    id: `local_${Date.now()}`,
    text: '',
    isNew: true,
    order_index: insertIndex,
  }
  scripts.value = [...scripts.value, newScript]
}

const handleReorderScripts = async (newOrder: string[]) => {
  if (!appStore.projectId) return
  
  try {
    const reorderedScripts = await reorderScripts(appStore.projectId, newOrder)
    scripts.value = reorderedScripts
    message.success('脚本顺序已更新')
  } catch (error) {
    console.error("重新排序脚本失败:", error)
    message.error("重新排序脚本失败")
  }
}

const handleConfirmScript = () => {
  if (scripts.value.length === 0) {
    message.error('请先生成或添加脚本内容')
    return
  }
  // Just navigate, generation will happen in the next step
  setCurrentTab('storyboard')
}

const handleRefreshScripts = async () => {
  if (!appStore.projectId) return
  
  try {
    const scriptsData = await getScripts(appStore.projectId)
    scripts.value = scriptsData
    console.log('脚本数据已刷新')
  } catch (error) {
    console.error("刷新脚本数据失败:", error)
    message.error("刷新脚本数据失败")
  }
}

// Main effect to kick off loading/polling
onMounted(() => {
  fetchProjectStatus()
})
</script>