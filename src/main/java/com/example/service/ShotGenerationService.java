package com.example.service;

import com.example.entity.Project;
import com.example.entity.ScriptShot;
import com.example.repository.ProjectRepository;
import com.example.repository.ScriptShotRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 镜头生成服务类
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShotGenerationService {

    private final ProjectRepository projectRepository;
    private final ScriptShotRepository scriptShotRepository;

    /**
     * 异步处理镜头生成
     */
    @Async
    @Transactional
    public void processShotGenerationAsync(String projectId) {
        try {
            log.info("开始后台镜头生成处理: {}", projectId);
            
            // 获取项目数据
            Project project = projectRepository.findById(projectId).orElse(null);
            if (project == null) {
                throw new RuntimeException("项目 " + projectId + " 在后台任务中未找到");
            }
            
            // 获取所有脚本
            List<ScriptShot> scripts = scriptShotRepository.findByProjectIdOrderByOrderIndexAsc(projectId);
            if (scripts.isEmpty()) {
                throw new RuntimeException("项目没有找到脚本");
            }
            
            log.info("项目 {} 找到 {} 个脚本，开始生成镜头", projectId, scripts.size());
            
            // 模拟镜头生成时间（5秒）
            Thread.sleep(3000);
            
            // 为每个脚本生成图片URL
            for (ScriptShot script : scripts) {
                String imageUrl = generateImageUrl();
                script.setImageUrl(imageUrl);
                // todo fix
                script.setImagePath("");
                script.setUpdatedAt(LocalDateTime.now());
            }
            
            // 批量保存脚本
            scriptShotRepository.saveAll(scripts);
            
            // 更新项目状态
            project.setShotGenerationStatus(Project.GenerationStatus.completed);
            projectRepository.save(project);
            
            log.info("✅ 项目 {} 镜头生成状态已设置为 'completed'", projectId);
            
        } catch (Exception e) {
            log.error("项目 {} 后台镜头生成失败: {}", projectId, e.getMessage(), e);
            
            // 更新失败状态
            Project project = projectRepository.findById(projectId).orElse(null);
            if (project != null) {
                project.setShotGenerationStatus(Project.GenerationStatus.failed);
                projectRepository.save(project);
            }
        }
    }

    /**
     * 生成随机图片URL
     */
    private String generateImageUrl() {
        String seed = UUID.randomUUID().toString().substring(0, 5);
        return String.format("https://picsum.photos/seed/%s/400/225", seed);
    }
} 