<template>
  <MainLayout />
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '../stores/app'
import MainLayout from '../components/layout/MainLayout.vue'

const route = useRoute()
const appStore = useAppStore()

const updateTabFromRoute = () => {
  // 从路由meta信息设置当前标签页
  const tab = route.meta?.tab as string
  if (tab) {
    appStore.setCurrentTab(tab)
  } else {
    // 如果没有meta信息，从URL查询参数获取（向后兼容）
    const queryTab = route.query.tab as string
    if (queryTab) {
      appStore.setCurrentTab(queryTab)
    }
  }
}

onMounted(() => {
  // 设置当前项目ID
  const projectId = route.params.projectId as string
  if (projectId) {
    appStore.setProjectId(projectId)
  }

  // 初始化tab
  updateTabFromRoute()
})

// 监听路由变化，更新当前tab
watch(() => route.meta?.tab, () => {
  updateTabFromRoute()
}, { immediate: false })
</script> 