<template>
  <div class="progress-steps">
    <a-steps 
      :current="currentStep" 
      :direction="direction"
      :size="size"
      :status="status"
    >
      <a-step
        v-for="(step, index) in steps"
        :key="index"
        :title="step.title"
        :description="step.description"
        :status="getStepStatus(index)"
      >
        <template v-if="step.icon" #icon>
          <component :is="step.icon" />
        </template>
      </a-step>
    </a-steps>
  </div>
</template>

<script setup lang="ts">
export interface Step {
  title: string
  description?: string
  icon?: any
}

interface Props {
  steps: Step[]
  currentStep: number
  direction?: 'horizontal' | 'vertical'
  size?: 'default' | 'small'
  status?: 'wait' | 'process' | 'finish' | 'error'
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'horizontal',
  size: 'default',
  status: 'process'
})

const getStepStatus = (index: number) => {
  if (index < props.currentStep) return 'finish'
  if (index === props.currentStep) return props.status
  return 'wait'
}
</script>

<style scoped>
.progress-steps {
  width: 100%;
}
</style> 