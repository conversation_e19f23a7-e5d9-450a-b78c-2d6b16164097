import { useState, useCallback, useRef, useEffect } from 'react';
import { App } from 'antd';
import { useApp } from '../../context/AppContext.jsx';
import { useApi } from '../../hooks/useApi.js';
import Button from '../common/Button';

const ScriptItem = ({ script, index, setScripts }) => {
  const { state: appState } = useApp();
  const { message, modal } = App.useApp();
  const { addScript, updateScript, deleteScript, regenerateScript } = useApi();

  const [currentText, setCurrentText] = useState(script.text);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);

  const textareaRef = useRef(null);
  
  // Automatically adjust textarea height
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'inherit';
      const scrollHeight = textareaRef.current.scrollHeight;
      textareaRef.current.style.height = `${scrollHeight}px`;
    }
  }, [currentText]);
  
  // Focus new script item
  useEffect(() => {
    if (script.isNew && textareaRef.current) {
        textareaRef.current.focus();
    }
  }, [script.isNew]);

  const handleSave = useCallback(async () => {
    if (currentText.trim() === script.text && !script.isNew) {
      return; // No change
    }
    
    if (currentText.trim() === '') {
        // If it's a new script and it's empty, just remove it from the list
        if(script.isNew) {
            setScripts(prev => prev.filter(s => s.id !== script.id));
        }
        return;
    }

    setIsSaving(true);
    try {
      let updatedScript;
      if (script.isNew) {
        updatedScript = await addScript({
          projectId: appState.projectId,
          text: currentText,
          order_index: script.order_index,
        });
        message.success('新脚本已添加');
      } else {
        updatedScript = await updateScript(script.id, { text: currentText });
        message.success('脚本已更新');
      }

      setScripts(prev => 
        prev.map(s => s.id === script.id ? { ...updatedScript, isNew: false } : s)
      );
    } catch (error) {
      console.error("Failed to save script:", error);
      message.error('保存失败，请重试');
      setCurrentText(script.text); // Revert on failure
    } finally {
      setIsSaving(false);
    }
  }, [currentText, script, appState.projectId, addScript, updateScript, setScripts, message]);

  const handleDelete = useCallback(() => {
    modal.confirm({
      title: '确认删除脚本？',
      content: '此操作不可撤销。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        let originalScripts;
        setScripts(prev => {
            originalScripts = prev;
            return prev.filter(s => s.id !== script.id)
        });

        setIsDeleting(true);
        try {
            if (!script.isNew) {
                await deleteScript(script.id);
            }
            message.success('脚本已删除');
        } catch (error) {
            console.error("Failed to delete script:", error);
            message.error('删除失败，请重试');
            setScripts(originalScripts); // Revert on failure
        } finally {
            setIsDeleting(false);
        }
      },
    });
  }, [script, deleteScript, setScripts, message, modal]);

  const handleRegenerate = useCallback(() => {
    modal.confirm({
        title: '确认重新生成脚本？',
        content: '当前脚本内容将被覆盖。',
        okText: '确认生成',
        cancelText: '取消',
        onOk: async () => {
            setIsRegenerating(true);
            try {
              const regenerated = await regenerateScript(script.id);
              setCurrentText(regenerated.text);
              setScripts(prev => 
                prev.map(s => s.id === script.id ? { ...s, text: regenerated.text } : s)
              );
              message.success('脚本已重新生成');
            } catch (error) {
              console.error("Failed to regenerate script:", error);
              message.error('重新生成失败');
            } finally {
              setIsRegenerating(false);
            }
        }
    });
  }, [script.id, regenerateScript, setScripts, message, modal]);


  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm flex items-start space-x-4">
      <div className="text-sm font-bold text-gray-500 bg-gray-100 rounded-full h-8 w-8 flex items-center justify-center flex-shrink-0 mt-1">
        {index + 1}
      </div>
      <textarea
        ref={textareaRef}
        value={currentText}
        onChange={(e) => setCurrentText(e.target.value)}
        onBlur={handleSave}
        className="w-full p-2 rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 resize-none overflow-hidden transition-all duration-150"
        rows={1}
        placeholder="请输入脚本内容..."
        disabled={isSaving || isDeleting || isRegenerating}
      />
      <div className="flex flex-col space-y-2">
        {!script.isNew && (
            <Button
                icon={<span className="material-icons text-lg">autorenew</span>}
                onClick={handleRegenerate}
                disabled={isRegenerating || isSaving || isDeleting}
                variant="secondary"
                tooltip="重新生成"
                loading={isRegenerating}
            />
        )}
        <Button 
          icon={<span className="material-icons text-lg">delete</span>} 
          onClick={handleDelete}
          disabled={isDeleting || isSaving}
          variant="danger"
          tooltip="删除"
        />
      </div>
    </div>
  );
};

export default ScriptItem;
