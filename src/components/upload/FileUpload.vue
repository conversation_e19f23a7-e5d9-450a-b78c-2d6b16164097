<template>
  <div class="mb-6">
    <label class="block text-sm font-medium text-gray-700 mb-2">
      上传素材文件
    </label>
    
    <!-- 拖拽上传区域 -->
    <div
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      :class="[
        'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
        isDragOver 
          ? 'border-blue-500 bg-blue-50' 
          : 'border-gray-300 hover:border-gray-400'
      ]"
    >
      <input
        ref="fileInput"
        type="file"
        multiple
        accept=".txt"
        @change="handleFileSelect"
        class="hidden"
      />
      
      <div class="flex flex-col items-center">
        <CloudArrowUpIcon class="h-12 w-12 text-gray-400 mb-2" />
        <p class="text-sm text-gray-600 mb-2">
          拖拽文件到此处，或
          <button
            @click="fileInput?.click()"
            class="text-blue-500 hover:text-blue-600 font-medium"
          >
            点击选择文件
          </button>
        </p>
        <p class="text-xs text-gray-400">
          支持txt格式文本
        </p>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  CloudArrowUpIcon, 
} from '@heroicons/vue/24/outline'
import { useApi } from '../../composables/useApi' 
import { useRoute } from 'vue-router' 

const route = useRoute()
const projectId = route.params.projectId as string  

interface FileItem {
  file: File
  status: 'pending' | 'uploading' | 'uploaded' | 'error'
  progress: number
  id?: string
  path?: string
}

const emit = defineEmits<{
  'update:files': []
}>()

const isDragOver = ref(false)
const fileInput = ref<HTMLInputElement>()

const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
  
  const droppedFiles = Array.from(e.dataTransfer?.files || [])
  processFiles(droppedFiles)
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const selectedFiles = Array.from(target.files || [])
  processFiles(selectedFiles)
}

const processFiles = (newFiles: File[]) => {
  const fileItems: FileItem[] = newFiles.map(file => ({
    file,
    status: 'pending',
    progress: 0
  }))

  // 模拟上传过程
  fileItems.forEach((fileItem, index) => {
    simulateUpload(fileItem)
  })
}

const simulateUpload = async (fileItem: FileItem) => {
  fileItem.status = 'uploading'
  
  const interval = setInterval(() => {
    fileItem.progress += Math.random() * 20
    
    if (fileItem.progress >= 100) {
      fileItem.progress = 100
      fileItem.status = 'uploaded'
      clearInterval(interval)
    }
  }, 200)

  try {
    // 实际上传
    const res = await uploadFile(fileItem.file)
    fileItem.status = 'uploaded'
    fileItem.progress = 100
    emit('update:files')
  } catch (e) {
    fileItem.status = 'error'
    fileItem.progress = 0
  }
}

const uploadFile = async (file: File) => {
  return await useApi().uploadFile(projectId, file)
}
</script> 