<template>
  <a-modal
    :open="isOpen"
    :title="modalTitle"
    :width="800"
    @cancel="onClose"
    @ok="handleConfirm"
    :ok-button-props="{ disabled: isConfiguring }"
  >
    <div v-if="shotData" class="space-y-6">
      <!-- 镜头信息 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="font-medium text-gray-900 mb-2">镜头信息</h4>
        <div class="flex items-start space-x-4">
          <img
            :src="shotData.shot.imageUrl"
            :alt="`镜头 ${shotData.shotId}`"
            class="w-24 h-16 object-cover rounded"
          />
          <div>
            <p class="text-sm text-gray-700">{{ shotData.shot.scriptText }}</p>
          </div>
        </div>
      </div>

      <!-- 数字人类型 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">数字人类型</label>
        <a-select
          v-model:value="avatarConfig.avatarType"
          class="w-full"
          placeholder="选择数字人"
        >
          <a-select-option value="none">
            <div class="flex items-center">
              <span class="text-base mr-2 flex items-center">🚫</span>
              <div class="text-sm font-medium text-center">不使用数字人</div>
            </div>
          </a-select-option>
          <a-select-option 
            v-for="avatar in avatarOptions" 
            :key="avatar.value" 
            :value="avatar.value"
          >
            <div class="flex items-center">
              <span class="text-base mr-2 flex items-center">{{ avatar.icon }}</span>
              <div class="text-sm font-medium text-center">{{ `${avatar.label} ${avatar.description}` }}</div>
            </div>
          </a-select-option>
        </a-select>
      </div>

      <!-- 位置设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">位置</label>
        <a-radio-group v-model:value="avatarConfig.position" :disabled="avatarConfig.avatarType === 'none'">
          <a-radio value="left">左侧</a-radio>
          <a-radio value="center">中央</a-radio>
          <a-radio value="right">右侧</a-radio>
        </a-radio-group>
      </div>

      <!-- 大小设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">大小</label>
        <a-radio-group v-model:value="avatarConfig.size" :disabled="avatarConfig.avatarType === 'none'">
          <a-radio value="small">小</a-radio>
          <a-radio value="medium">中</a-radio>
          <a-radio value="large">大</a-radio>
        </a-radio-group>
      </div>

      <!-- 背景设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">背景</label>
        <a-radio-group v-model:value="avatarConfig.background" :disabled="avatarConfig.avatarType === 'none'">
          <a-radio value="transparent">透明</a-radio>
          <a-radio value="blurred">模糊</a-radio>
          <a-radio value="solid">纯色</a-radio>
        </a-radio-group>
      </div>

    </div>

    <template #footer>
      <div class="flex justify-between">
        <a-button @click="onClose">取消</a-button>
        <div class="space-x-2">
          <a-button @click="handlePreview" :disabled="isConfiguring || avatarConfig.avatarType === 'none'">
            预览
          </a-button>
          <a-button 
            type="primary" 
            @click="handleConfirm"
            :disabled="isConfiguring"
          >
            {{ isConfiguring ? '配置中...' : (avatarConfig.avatarType === 'none' ? '确认不使用数字人' : '确认配置') }}
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Shot {
  id: number
  scriptText: string
  imageUrl: string
}

interface ShotData {
  shotId: string
  shot: Shot
}

interface AvatarConfig {
  avatarType: string
  position: string
  size: string
  background: string
  lighting: string
  expression: string
}

interface Props {
  isOpen: boolean
  onClose: () => void
  shotData?: ShotData
  onConfigure: (shotId: number) => void
}

const props = defineProps<Props>()

const isConfiguring = ref(false)

// 默认配置
const avatarConfig = ref<AvatarConfig>({
  avatarType: 'none',
  position: 'center',
  size: 'medium',
  background: 'transparent',
  lighting: 'natural',
  expression: 'neutral'
})

// 数字人选项列表
const avatarOptions = ref<any[]>([])

const modalTitle = computed(() => {
  return props.shotData ? `镜头 ${props.shotData.shotId} - 数字人配置` : '数字人配置'
})

// 重置配置
watch(() => props.isOpen, (newVal) => {
  if (newVal) {
    avatarConfig.value = {
      avatarType: 'none',
      position: 'center',
      size: 'medium',
      background: 'transparent',
      lighting: 'natural',
      expression: 'neutral'
    }
    isConfiguring.value = false
  }
})

const handlePreview = () => {
  // 模拟预览功能
  console.log('预览配置:', avatarConfig.value)
}

const handleConfirm = async () => {
  if (!props.shotData) return
  
  isConfiguring.value = true
  
  // 模拟配置过程
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  props.onConfigure(props.shotData.shot.id)
  isConfiguring.value = false
  props.onClose()
}
</script> 