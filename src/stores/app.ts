import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

export interface UploadedFile {
  id: string
  name: string
  type: string
  size: number
  url: string
  uploadProgress?: number
  isProcessing?: boolean
}

export interface Script {
  id: number
  text: string
}

export interface Shot {
  id: number
  number: number
  scriptText: string
  imageUrl: string
}

export interface GeneratedVideo {
  [key: string]: string
}

export interface ExportSettings {
  quality: string
  format: string
}

export interface Task {
  id: string
  status: string
  type: string
  progress?: number
  result?: any
}

export interface ProjectData {
  id: string
  name: string
  theme: string
  requirements: string
  textMaterial: string
  scriptGenerationStatus: string
  shotGenerationStatus: string
  compositionStatus: string
  finalVideoUrl: string
  isDraft: boolean
  avatarId: string
  voiceId: string
  createdAt: string
  updatedAt: string
  assets: UploadedFile[]
  shots: Shot[]
}

export const useAppStore = defineStore('app', () => {
  // State
  const currentTab = ref('upload')
  const currentProject = ref<ProjectData | null>(null)
  const projectName = ref('')
  const projectTheme = ref('')
  const projectRequirements = ref('')
  const materialSource = ref('upload')
  const uploadedFiles = ref<UploadedFile[]>([])
  const textMaterial = ref('')
  
  const scripts = ref<Script[]>([
    {
      id: 1,
      text: '清晨的阳光透过百叶窗，洒在整洁的办公桌上。一台笔记本电脑屏幕亮着，显示着公司的Logo。'
    },
    {
      id: 2,
      text: '特写：手指轻快地在键盘上敲击，屏幕上代码飞速滚动。'
    },
    {
      id: 3,
      text: '镜头切换至会议室，团队成员围坐在一起，激烈地讨论着。白板上写满了各种想法和流程图。'
    }
  ])

  const shots = ref<Shot[]>([])
  const selectedVoice = ref('professional-female')
  const voiceGenerated = ref(false)
  const videoGenerated = ref(false)
  const generatedVideos = ref<GeneratedVideo>({})
  const selectedAvatar = ref('emma')
  const avatarConfigured = ref(false)
  const videoComposed = ref(false)
  const composedVideoUrl = ref('')
  
  const exportSettings = reactive<ExportSettings>({
    quality: '1080p',
    format: 'MP4'
  })

  // API related state
  const projectId = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const availableVoices = ref<any[]>([])
  const availableAvatars = ref<any[]>([])
  const tasks = ref<Record<string, Task>>({})
  const apiConnected = ref(false)

  // Actions
  const setCurrentTab = (tab: string) => {
    currentTab.value = tab
  }

  const setCurrentProject = (project: ProjectData | null) => {
    currentProject.value = project
    if (project) {
      setProjectId(project.id)
      updateProjectInfo({
        projectName: project.name,
        projectTheme: project.theme,
        projectRequirements: project.requirements,
      })
      setTextMaterial(project.textMaterial)
      shots.value = project.shots
    }
  }

  const updateProjectInfo = (info: Partial<{
    projectName: string
    projectTheme: string
    projectRequirements: string
  }>) => {
    if (info.projectName !== undefined) projectName.value = info.projectName
    if (info.projectTheme !== undefined) projectTheme.value = info.projectTheme
    if (info.projectRequirements !== undefined) projectRequirements.value = info.projectRequirements
  }

  const setMaterialSource = (source: string) => {
    materialSource.value = source
  }

  const addUploadedFile = (file: UploadedFile) => {
    uploadedFiles.value.push(file)
  }

  const setUploadedFiles = (files: UploadedFile[]) => {
    uploadedFiles.value = files
  }

  const updateUploadedFile = (file: UploadedFile) => {
    const index = uploadedFiles.value.findIndex(f => f.id === file.id)
    if (index !== -1) {
      uploadedFiles.value[index] = file
    }
  }

  const removeUploadedFile = (fileId: string) => {
    uploadedFiles.value = uploadedFiles.value.filter(file => file.id !== fileId)
  }

  const setTextMaterial = (text: string) => {
    textMaterial.value = text
  }

  const setScripts = (newScripts: Script[]) => {
    scripts.value = newScripts
  }

  const addScript = (text: string = '') => {
    const newScript: Script = {
      id: Date.now(),
      text
    }
    scripts.value.push(newScript)
  }

  const updateScript = (id: number, text: string) => {
    const script = scripts.value.find(s => s.id === id)
    if (script) {
      script.text = text
    }
  }

  const deleteScript = (id: number) => {
    scripts.value = scripts.value.filter(s => s.id !== id)
  }

  const generateShots = (shotData?: Shot[]) => {
    if (shotData) {
      shots.value = shotData
    } else {
      shots.value = scripts.value.map((script, index) => ({
        id: script.id,
        number: index + 1,
        scriptText: script.text,
        imageUrl: `https://picsum.photos/seed/${Math.floor(Math.random() * 1000)}/400/225`
      }))
    }
  }

  const regenerateShot = (id: number, newImageUrl: string) => {
    const shot = shots.value.find(s => s.id === id)
    if (shot) {
      shot.imageUrl = newImageUrl
    }
  }

  const setSelectedVoice = (voice: string) => {
    selectedVoice.value = voice
  }

  const generateVoice = () => {
    voiceGenerated.value = true
  }

  const generateVideo = () => {
    videoGenerated.value = true
  }

  const setGeneratedVideos = (videos: GeneratedVideo) => {
    generatedVideos.value = { ...generatedVideos.value, ...videos }
  }

  const setSelectedAvatar = (avatar: string) => {
    selectedAvatar.value = avatar
  }

  const configureAvatar = () => {
    avatarConfigured.value = true
  }

  const composeVideo = (url: string = '') => {
    videoComposed.value = true
    composedVideoUrl.value = url
  }

  const updateExportSettings = (settings: Partial<ExportSettings>) => {
    Object.assign(exportSettings, settings)
  }

  // API related actions
  const setProjectId = (id: string | null) => {
    projectId.value = id
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const setAvailableVoices = (voices: any[]) => {
    availableVoices.value = voices
  }

  const setAvailableAvatars = (avatars: any[]) => {
    availableAvatars.value = avatars
  }

  const updateTask = (taskId: string, task: Task) => {
    tasks.value[taskId] = task
  }

  const setApiConnected = (connected: boolean) => {
    apiConnected.value = connected
  }

  const clearProjectData = () => {
    projectId.value = null
    uploadedFiles.value = []
    shots.value = []
    voiceGenerated.value = false
    videoGenerated.value = false
    generatedVideos.value = {}
    avatarConfigured.value = false
    videoComposed.value = false
    composedVideoUrl.value = ''
    tasks.value = {}
    currentProject.value = null
  }

  return {
    // State
    currentTab,
    currentProject,
    projectName,
    projectTheme,
    projectRequirements,
    materialSource,
    uploadedFiles,
    textMaterial,
    scripts,
    shots,
    selectedVoice,
    voiceGenerated,
    videoGenerated,
    generatedVideos,
    selectedAvatar,
    avatarConfigured,
    videoComposed,
    composedVideoUrl,
    exportSettings,
    projectId,
    isLoading,
    error,
    availableVoices,
    availableAvatars,
    tasks,
    apiConnected,

    // Actions
    setCurrentTab,
    setCurrentProject,
    updateProjectInfo,
    setMaterialSource,
    addUploadedFile,
    setUploadedFiles,
    updateUploadedFile,
    removeUploadedFile,
    setTextMaterial,
    setScripts,
    addScript,
    updateScript,
    deleteScript,
    generateShots,
    regenerateShot,
    setSelectedVoice,
    generateVoice,
    generateVideo,
    setGeneratedVideos,
    setSelectedAvatar,
    configureAvatar,
    composeVideo,
    updateExportSettings,
    setProjectId,
    setLoading,
    setError,
    setAvailableVoices,
    setAvailableAvatars,
    updateTask,
    setApiConnected,
    clearProjectData
  }
}) 