package com.example.config.openapi;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Profile("dev")
@Configuration
public class OpenAPIConfig {

    @Value("${springdoc.api-info.title:Java Project API}")
    private String apiTitle;

    @Value("${springdoc.api-info.version:0.0.1-SNAPSHOT}")
    private String apiVersion;

    @Value("${springdoc.api-info.description:API documentation for Java Project Template}")
    private String apiDescription;

    @Value("${springdoc.api-info.terms-of-service:urn:tos}")
    private String termsOfServiceUrl;

    @Value("${springdoc.api-info.license.name:Apache 2.0}")
    private String licenseName;

    @Value("${springdoc.api-info.license.url:https://www.apache.org/licenses/LICENSE-2.0.html}")
    private String licenseUrl;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(apiTitle)
                        .version(apiVersion)
                        .description(apiDescription)
                        .termsOfService(termsOfServiceUrl)
                        .license(new License().name(licenseName).url(licenseUrl)));
    }
} 