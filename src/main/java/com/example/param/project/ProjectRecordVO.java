package com.example.param.project;

import com.example.entity.Project;
import com.example.param.asset.AssetRecordVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目Record VO
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
public record ProjectRecordVO(
        String id,
        String name,
        String theme,
        String requirements,
        String textMaterial,
        String scriptGenerationStatus,
        String shotGenerationStatus,
        String compositionStatus,
        String finalVideoUrl,
        Boolean isDraft,
        String avatarId,
        String voiceId,
        LocalDateTime createdAt,
        LocalDateTime updatedAt,
        List<AssetRecordVO> assets
) {
    public static ProjectRecordVO from(Project project, List<AssetRecordVO> assets) {
        return new ProjectRecordVO(
            project.getId(),
            project.getName(),
            project.getTheme(),
            project.getRequirements(),
            project.getTextMaterial(),
            project.getScriptGenerationStatus().name(),
            project.getShotGenerationStatus().name(),
            project.getCompositionStatus().name(),
            project.getFinalVideoUrl(),
            project.getIsDraft(),
            project.getAvatarId(),
            project.getVoiceId(),
            project.getCreatedAt(),
            project.getUpdatedAt(),
            assets
        );
    }
} 