package com.example.utils;

import lombok.extern.slf4j.Slf4j;
import net.bramp.ffmpeg.FFprobe;
import net.bramp.ffmpeg.probe.FFmpegProbeResult;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 视频验证工具类
 * 用于验证视频文件的完整性、时长等信息
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
public final class VideoValidationUtils {

    private VideoValidationUtils() {
        // 防止实例化
    }

    /**
     * 验证视频文件是否存在且有效
     *
     * @param videoPath 视频文件路径
     * @return 如果文件存在且大小大于0则返回true
     */
    public static boolean isValidVideoFile(String videoPath) {
        try {
            Path path = Paths.get(videoPath);
            return Files.exists(path) && Files.size(path) > 0;
        } catch (IOException e) {
            log.warn("检查视频文件失败: {}", videoPath, e);
            return false;
        }
    }

    /**
     * 获取视频文件的时长（秒）
     *
     * @param videoPath 视频文件路径
     * @return 视频时长（秒）
     * @throws IOException 如果无法读取视频信息
     */
    public static double getVideoDuration(String videoPath) throws IOException {
        FFprobe ffprobe = new FFprobe();
        FFmpegProbeResult probeResult = ffprobe.probe(videoPath);
        return probeResult.getFormat().duration;
    }

    /**
     * 获取视频文件的详细信息
     *
     * @param videoPath 视频文件路径
     * @return 视频信息对象
     * @throws IOException 如果无法读取视频信息
     */
    public static VideoInfo getVideoInfo(String videoPath) throws IOException {
        FFprobe ffprobe = new FFprobe();
        FFmpegProbeResult probeResult = ffprobe.probe(videoPath);
        
        return VideoInfo.builder()
                .path(videoPath)
                .duration(probeResult.getFormat().duration)
                .size(probeResult.getFormat().size)
                .bitRate(probeResult.getFormat().bit_rate)
                .formatName(probeResult.getFormat().format_name)
                .build();
    }

    /**
     * 验证视频时长是否在合理范围内
     *
     * @param videoPath 视频文件路径
     * @param expectedDuration 期望时长（秒）
     * @param tolerance 容差（秒）
     * @return 如果时长在合理范围内返回true
     */
    public static boolean validateVideoDuration(String videoPath, double expectedDuration, double tolerance) {
        try {
            double actualDuration = getVideoDuration(videoPath);
            double difference = Math.abs(actualDuration - expectedDuration);
            
            log.debug("视频时长验证: 文件={}, 实际={}秒, 期望={}秒, 差异={}秒", 
                    videoPath, actualDuration, expectedDuration, difference);
            
            return difference <= tolerance;
        } catch (IOException e) {
            log.error("无法获取视频时长: {}", videoPath, e);
            return false;
        }
    }

    /**
     * 批量验证视频文件列表
     *
     * @param videoPaths 视频文件路径列表
     * @return 验证结果
     */
    public static ValidationResult validateVideoList(List<String> videoPaths) {
        ValidationResult result = new ValidationResult();
        
        for (String videoPath : videoPaths) {
            try {
                if (!isValidVideoFile(videoPath)) {
                    result.addError(videoPath, "文件不存在或为空");
                    continue;
                }
                
                VideoInfo info = getVideoInfo(videoPath);
                result.addValidVideo(info);
                
                // 检查异常时长（超过1小时的视频可能有问题）
                if (info.getDuration() > 3600) {
                    result.addWarning(videoPath, 
                            String.format("视频时长异常: %.2f秒 (%.2f小时)", 
                                    info.getDuration(), info.getDuration() / 3600));
                }
                
                // 检查文件大小（过小可能有问题）
                if (info.getSize() < 1024) { // 小于1KB
                    result.addWarning(videoPath, 
                            String.format("视频文件过小: %d bytes", info.getSize()));
                }
                
            } catch (IOException e) {
                result.addError(videoPath, "无法读取视频信息: " + e.getMessage());
            }
        }
        
        return result;
    }

    /**
     * 修复时长参数
     * 处理可能的毫秒值、异常值等
     *
     * @param rawDuration 原始时长值
     * @param defaultDuration 默认时长
     * @param maxDuration 最大允许时长
     * @return 修复后的时长（秒）
     */
    public static int fixDurationParameter(long rawDuration, int defaultDuration, int maxDuration) {
        if (rawDuration <= 0) {
            log.warn("时长参数无效: {}, 使用默认值: {}秒", rawDuration, defaultDuration);
            return defaultDuration;
        }
        
        if (rawDuration > maxDuration) {
            // 可能是毫秒值
            if (rawDuration > 60000) {
                int convertedDuration = (int) (rawDuration / 1000);
                if (convertedDuration <= maxDuration) {
                    log.warn("检测到毫秒值: {}, 转换为秒: {}", rawDuration, convertedDuration);
                    return convertedDuration;
                }
            }
            
            log.warn("时长参数过大: {}秒, 限制为: {}秒", rawDuration, maxDuration);
            return maxDuration;
        }
        
        return (int) rawDuration;
    }

    /**
     * 视频信息类
     */
    public static class VideoInfo {
        private String path;
        private double duration;
        private long size;
        private long bitRate;
        private String formatName;

        private VideoInfo(Builder builder) {
            this.path = builder.path;
            this.duration = builder.duration;
            this.size = builder.size;
            this.bitRate = builder.bitRate;
            this.formatName = builder.formatName;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public String getPath() { return path; }
        public double getDuration() { return duration; }
        public long getSize() { return size; }
        public long getBitRate() { return bitRate; }
        public String getFormatName() { return formatName; }

        public static class Builder {
            private String path;
            private double duration;
            private long size;
            private long bitRate;
            private String formatName;

            public Builder path(String path) { this.path = path; return this; }
            public Builder duration(double duration) { this.duration = duration; return this; }
            public Builder size(long size) { this.size = size; return this; }
            public Builder bitRate(long bitRate) { this.bitRate = bitRate; return this; }
            public Builder formatName(String formatName) { this.formatName = formatName; return this; }

            public VideoInfo build() {
                return new VideoInfo(this);
            }
        }

        @Override
        public String toString() {
            return String.format("VideoInfo{path='%s', duration=%.2fs, size=%d bytes, bitRate=%d, format='%s'}", 
                    path, duration, size, bitRate, formatName);
        }
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final List<VideoInfo> validVideos = new java.util.ArrayList<>();
        private final List<String> errors = new java.util.ArrayList<>();
        private final List<String> warnings = new java.util.ArrayList<>();

        public void addValidVideo(VideoInfo info) {
            validVideos.add(info);
        }

        public void addError(String videoPath, String error) {
            errors.add(videoPath + ": " + error);
        }

        public void addWarning(String videoPath, String warning) {
            warnings.add(videoPath + ": " + warning);
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }

        public List<VideoInfo> getValidVideos() { return validVideos; }
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }

        public double getTotalDuration() {
            return validVideos.stream().mapToDouble(VideoInfo::getDuration).sum();
        }

        public long getTotalSize() {
            return validVideos.stream().mapToLong(VideoInfo::getSize).sum();
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{valid=%d, errors=%d, warnings=%d, totalDuration=%.2fs}", 
                    validVideos.size(), errors.size(), warnings.size(), getTotalDuration());
        }
    }
}
