{"name": "video-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node app.js", "dev": "node app.js", "test": "node test-api.js", "test:api": "node test-api.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "better-sqlite3": "^12.1.1", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "form-data": "^4.0.3", "multer": "^2.0.1", "nanoid": "^3.3.4"}}