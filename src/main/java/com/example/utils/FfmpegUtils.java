package com.example.utils;

import net.bramp.ffmpeg.FFmpeg;
import net.bramp.ffmpeg.FFmpegExecutor;
import net.bramp.ffmpeg.FFprobe;
import net.bramp.ffmpeg.builder.FFmpegBuilder;
import net.bramp.ffmpeg.builder.FFmpegOutputBuilder;
import net.bramp.ffmpeg.probe.FFmpegProbeResult;
import net.bramp.ffmpeg.probe.FFmpegStream;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 一个使用 FFmpeg 进行常用视频处理任务的工具类。
 * 这个类依赖于 net.bramp:ffmpeg-cli-wrapper 库。
 *
 * 使用本工具类的前提是，ffmpeg 和 ffprobe 程序已经安装，并且其路径已在系统 PATH 环境变量中。
 */
public final class FfmpegUtils {

    private FfmpegUtils() {
        // 防止实例化
    }

    /**
     * 定义画中画（Picture-in-Picture）覆盖层位置的枚举。
     */
    public enum PipPosition {
        /**
         * 左上角
         */
        TOP_LEFT,
        /**
         * 右上角
         */
        TOP_RIGHT,
        /**
         * 左下角
         */
        BOTTOM_LEFT,
        /**
         * 右下角
         */
        BOTTOM_RIGHT
    }

    /**
     * 从一张静态图片创建指定时长的视频。
     *
     * @param imagePath         输入图片文件的路径。
     * @param outputPath        输出视频文件的路径。
     * @param durationInSeconds 视频的期望时长（秒）。
     * @throws IOException 如果 FFmpeg 执行出错。
     */
    public static void createImageToVideo(String imagePath, String outputPath, int durationInSeconds) throws IOException {
        FFmpeg ffmpeg = new FFmpeg();
        FFprobe ffprobe = new FFprobe();

        FFmpegBuilder builder = new FFmpegBuilder()
                .addExtraArgs("-loop", "1")
                .setInput(imagePath)
                .addOutput(outputPath)
                .setFormat("mp4")
                .setVideoCodec("libx264")
                .setVideoPixelFormat("yuv420p")
                .setDuration(durationInSeconds, TimeUnit.SECONDS)
                .done();

        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
    }

    /**
     * 将一个视频文件中的视频流和一个音频文件中的音频流合并。
     * 输出视频的时长将由两个输入中较短的那个决定。
     *
     * @param videoPath  输入视频文件的路径。
     * @param audioPath  输入音频文件的路径。
     * @param outputPath 输出视频文件的路径。
     * @throws IOException 如果 FFmpeg 执行出错。
     */
    public static void mergeVideoAndAudio(String videoPath, String audioPath, String outputPath) throws IOException {
        FFmpeg ffmpeg = new FFmpeg();
        FFprobe ffprobe = new FFprobe();

        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(videoPath)
                .addInput(audioPath)
                .addOutput(outputPath)
                .setVideoCodec("copy")
                .setAudioCodec("aac")
                .addExtraArgs("-shortest")
                .done();

        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
    }

    /**
     * 将一个小视频叠加到一个主视频上，在指定位置实现画中画效果。
     * 被叠加的视频会被缩放至主视频宽度的 1/4。
     *
     * @param mainVideoPath    主背景视频的路径。
     * @param overlayVideoPath 需要被叠加的视频的路径。
     * @param outputPath       输出视频文件的路径。
     * @param position         叠加层所在的角落位置。
     * @throws IOException 如果 FFmpeg 执行出错。
     */
    public static void createPictureInPicture(String mainVideoPath, String overlayVideoPath, String outputPath, PipPosition position) throws IOException {
        FFmpeg ffmpeg = new FFmpeg();
        FFprobe ffprobe = new FFprobe();

        // 探查主视频文件，检查是否存在音频流
        FFmpegProbeResult probeResult = ffprobe.probe(mainVideoPath);
        boolean hasAudio = probeResult.getStreams().stream()
                .anyMatch(s -> s.codec_type == FFmpegStream.CodecType.AUDIO);

        String overlayExpression;
        switch (position) {
            case TOP_LEFT:
                overlayExpression = "10:10";
                break;
            case TOP_RIGHT:
                overlayExpression = "main_w-overlay_w-10:10";
                break;
            case BOTTOM_LEFT:
                overlayExpression = "10:main_h-overlay_h-10";
                break;
            case BOTTOM_RIGHT:
            default:
                overlayExpression = "main_w-overlay_w-10:main_h-overlay_h-10";
                break;
        }

        String filter = "[1:v]scale=iw/4:-1[ov];[0:v][ov]overlay=" + overlayExpression + "[v]";

        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(mainVideoPath)
                .addInput(overlayVideoPath)
                .setComplexFilter(filter);

        FFmpegOutputBuilder outputBuilder = builder.addOutput(outputPath)
                .addExtraArgs("-map", "[v]"); // 只映射处理后的视频流

        // 如果主视频有音频，则映射它的音频流
        if (hasAudio) {
            outputBuilder
                    .addExtraArgs("-map", "0:a")
                    .setAudioCodec("copy");
        }

        outputBuilder.done();

        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
    }

    /**
     * 使用高性能的 FFmpeg concat demuxer 方法将多个视频片段拼接成一个文件。
     * 这个方法速度快且无损，因为它避免了重新编码。
     * 注意：当所有视频片段具有相同的编解码器、分辨率和帧率时，此方法效果最佳。
     *
     * @param videoPaths 一个包含待合并视频文件绝对路径的列表（按顺序）。
     * @param outputPath 最终合并视频文件的绝对路径。
     * @throws IOException 如果在文件操作或 FFmpeg 执行期间发生错误。
     */
    public static void concatenateVideos(List<String> videoPaths, String outputPath) throws IOException {
        Path tempListFile = Files.createTempFile("videolist-", ".txt");
        try {
            try (BufferedWriter writer = Files.newBufferedWriter(tempListFile, StandardOpenOption.WRITE)) {
                for (String videoPath : videoPaths) {
                    writer.write("file '" + videoPath.replace("'", "'\\''") + "'");
                    writer.newLine();
                }
            }

            FFmpeg ffmpeg = new FFmpeg();
            FFprobe ffprobe = new FFprobe();

            FFmpegBuilder builder = new FFmpegBuilder()
                    .setInput(tempListFile.toAbsolutePath().toString())
                    .addExtraArgs("-f", "concat", "-safe", "0")
                    .addOutput(outputPath)
                    .setVideoCodec("copy")
                    .done();

            FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
            executor.createJob(builder).run();
        } finally {
            Files.deleteIfExists(tempListFile);
        }
    }
}
