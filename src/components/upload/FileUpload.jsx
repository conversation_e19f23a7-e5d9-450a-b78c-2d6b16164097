import { Upload, But<PERSON> as Ant<PERSON><PERSON>on, Typography, Progress } from 'antd';
import { InboxOutlined, DeleteOutlined, CheckCircleOutlined, ExclamationCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { useFileUpload } from '../../hooks/useFileUpload';

const { Dragger } = Upload;
const { Text } = Typography;

const FileUpload = ({ files, setFiles }) => {
  const {
    uploading,
    handleFileSelect,
    removeFile
  } = useFileUpload({ files, setFiles });

  const uploadProps = {
    name: 'file',
    multiple: true,
    showUploadList: false,
    beforeUpload: (file) => {
      handleFileSelect([file]);
      return false; // 阻止自动上传
    },
    onDrop: (e) => {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        上传素材
      </label>

      {/* Upload Area */}
      <Dragger {...uploadProps} className="mb-4" disabled={uploading}>
        <p className="ant-upload-drag-icon">
          {uploading ? (
            <LoadingOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          ) : (
            <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          )}
        </p>
        <p className="ant-upload-text">
          {uploading ? '正在上传文件...' : '点击或拖拽文件到此处上传'}
        </p>
        <p className="ant-upload-hint">
          支持单个或批量上传。支持文档格式
        </p>
      </Dragger>

        {/* Uploaded Files List */}
        {files.length > 0 && (
          <div className="space-y-2">
            {files.map((file) => {
              const getStatusIcon = () => {
                switch (file.status) {
                  case 'uploading':
                    return <LoadingOutlined className="text-blue-500" />;
                  case 'uploaded':
                    return <CheckCircleOutlined className="text-green-500" />;
                  case 'error':
                    return <ExclamationCircleOutlined className="text-red-500" />;
                  default:
                    return null;
                }
              };

              const getStatusText = () => {
                switch (file.status) {
                  case 'uploading':
                    return '上传中...';
                  case 'uploaded':
                    return '上传成功';
                  case 'error':
                    return '上传失败';
                  case 'pending':
                    return '等待上传';
                  default:
                    return file.formattedSize;
                }
              };

              return (
                <div
                  key={file.id}
                  className={`flex items-center justify-between p-3 rounded-md border ${
                    file.status === 'error' 
                      ? 'bg-red-50 border-red-200' 
                      : file.status === 'uploaded'
                      ? 'bg-green-50 border-green-200'
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Text strong className="block" title={file.name}>
                        {file.name}
                      </Text>
                      {getStatusIcon()}
                    </div>
                    <Text type="secondary" className="text-xs">
                      {getStatusText()}
                    </Text>
                    {file.status === 'uploading' && (
                      <Progress 
                        percent={50} 
                        showInfo={false} 
                        size="small"
                        className="mt-1"
                      />
                    )}
                  </div>
                  <AntButton
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => removeFile(file.id)}
                    title="删除文件"
                    size="small"
                    disabled={file.status === 'uploading'}
                  />
                </div>
              );
            })}
          </div>
        )}
    </div>
  );
};

export default FileUpload;
