package com.example.param.project;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 项目创建参数
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Data
public class ProjectCreateParam {

    @NotBlank(message = "项目名称不能为空")
    @Size(max = 200, message = "项目名称长度不能超过200字符")
    private String name;

    @Size(max = 500, message = "主题长度不能超过500字符")
    private String theme;

    private String requirements;

    private String textMaterial;

    private Boolean isDraft = false;

    private String avatarId;

    private String voiceId;
} 