package com.example.param.script;

import com.example.entity.ScriptShot;

import java.time.LocalDateTime;

/**
 * 脚本镜头Record VO
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
public record ScriptShotRecordVO(
        String id,
        String projectId,
        String text,
        Integer orderIndex,
        Integer shotNumber,
        String imageUrl,
        String audioUrl,
        String videoUrl,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
) {
    public static ScriptShotRecordVO from(ScriptShot scriptShot) {
        return new ScriptShotRecordVO(
            scriptShot.getId(),
            scriptShot.getProjectId(),
            scriptShot.getText(),
            scriptShot.getOrderIndex(),
            scriptShot.getShotNumber(),
            scriptShot.getImageUrl(),
            scriptShot.getAudioUrl(),
            scriptShot.getVideoUrl(),
            scriptShot.getCreatedAt(),
            scriptShot.getUpdatedAt()
        );
    }
} 