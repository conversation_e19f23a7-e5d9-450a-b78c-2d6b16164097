package com.example.entity;

import com.example.common.enums.BaseEnum;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 项目实体
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "t_project")
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class Project {

    @Id
    @Column(length = 50)
    private String id;

    @Column(nullable = false, length = 200)
    private String name;

    @Column(length = 500)
    private String theme;

    @Column(columnDefinition = "TEXT")
    private String requirements;

    @Column(name = "text_material", columnDefinition = "TEXT")
    private String textMaterial;

    @Enumerated(EnumType.STRING)
    @Column(name = "script_generation_status", nullable = false, length = 20)
    private GenerationStatus scriptGenerationStatus = GenerationStatus.not_started;

    @Enumerated(EnumType.STRING)
    @Column(name = "shot_generation_status", nullable = false, length = 20)
    private GenerationStatus shotGenerationStatus = GenerationStatus.not_started;

    @Enumerated(EnumType.STRING)
    @Column(name = "composition_status", nullable = false, length = 20)
    private GenerationStatus compositionStatus = GenerationStatus.not_started;

    @Column(name = "final_video_url", length = 500)
    private String finalVideoUrl;

    @Column(name = "is_draft", nullable = false)
    private Boolean isDraft = false;

    @Column(name = "avatar_id", length = 50)
    private String avatarId;

    @Column(name = "voice_id", length = 50)
    private String voiceId;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 生成状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum GenerationStatus implements BaseEnum {
        not_started("未开始"),
        in_progress("生成中"),
        completed("已完成"),
        failed("失败");

        private final String label;
    }
} 