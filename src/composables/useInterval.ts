import { ref, onUnmounted, watch, type Ref } from 'vue'

export function useInterval(callback: () => void, delay: Ref<number | null>) {
  const intervalId = ref<number | null>(null)

  const clearCurrentInterval = () => {
    if (intervalId.value) {
      clearInterval(intervalId.value)
      intervalId.value = null
    }
  }

  const startInterval = () => {
    if (delay.value !== null && delay.value > 0) {
      clearCurrentInterval()
      intervalId.value = setInterval(callback, delay.value)
    }
  }

  watch(
    delay,
    (newDelay) => {
      clearCurrentInterval()
      if (newDelay !== null && newDelay > 0) {
        startInterval()
      }
    },
    { immediate: true }
  )

  onUnmounted(() => {
    clearCurrentInterval()
  })

  return {
    clearInterval: clearCurrentInterval,
    startInterval
  }
}