# 测试环境配置
spring:
  # 测试数据源配置 - PostgreSQL
  datasource:
    url: **************************************************
    driver-class-name: org.postgresql.Driver
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 5
  
  # JPA测试配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false

# 测试日志配置
logging:
  level:
    root: WARN
    com.example: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN 