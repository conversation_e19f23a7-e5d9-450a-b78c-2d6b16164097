package com.example.service;

import com.example.common.exception.BusinessException;
import com.example.common.response.ResultCode;
import com.example.entity.Task;
import com.example.param.task.TaskCreateParam;
import com.example.param.task.TaskRecordVO;
import com.example.repository.ProjectRepository;
import com.example.repository.TaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 任务服务类
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskService {

    private final TaskRepository taskRepository;
    private final ProjectRepository projectRepository;

    /**
     * 创建任务
     */
    @Transactional
    public TaskRecordVO createTask(TaskCreateParam param) {
        log.info("创建任务: {} - {}", param.getTaskType(), param.getProjectId());
        
        // 验证项目是否存在
        if (StringUtils.hasText(param.getProjectId())) {
            if (!projectRepository.existsById(param.getProjectId())) {
                throw new BusinessException(ResultCode.NOT_FOUND, "项目不存在");
            }
        }
        
        Task task = new Task();
        task.setId("task_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16));
        task.setProjectId(param.getProjectId());
        task.setType(param.getTaskType());
        task.setStatus(Task.TaskStatus.PROCESSING);
        
        task = taskRepository.save(task);
        
        // 异步处理任务
        final String taskId = task.getId();
        final Task.TaskType taskType = param.getTaskType();
        final String taskData = param.getTaskData();
        CompletableFuture.runAsync(() -> processTask(taskId, taskType, taskData));
        
        log.info("任务创建成功: {} - {}", task.getId(), task.getType());
        return TaskRecordVO.from(task);
    }

    /**
     * 获取任务详情
     */
    public TaskRecordVO getTaskById(String taskId) {
        log.debug("获取任务详情: {}", taskId);
        
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务不存在"));
        
        return TaskRecordVO.from(task);
    }

    /**
     * 获取项目的任务列表
     */
    public List<TaskRecordVO> getProjectTasks(String projectId) {
        log.debug("获取项目任务列表: {}", projectId);
        
        List<Task> tasks = taskRepository.findByProjectIdOrderByCreatedAtDesc(projectId);
        return tasks.stream()
                .map(TaskRecordVO::from)
                .toList();
    }

    /**
     * 取消任务
     */
    @Transactional
    public void cancelTask(String taskId) {
        log.info("取消任务: {}", taskId);
        
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "任务不存在"));
        
        if (task.getStatus() == Task.TaskStatus.COMPLETED) {
            throw new BusinessException(ResultCode.OPERATION_FAILED, "任务已完成，无法取消");
        }
        
        task.setStatus(Task.TaskStatus.CANCELED);
        taskRepository.save(task);
        
        log.info("任务取消成功: {}", taskId);
    }

    /**
     * 异步处理任务
     */
    private void processTask(String taskId, Task.TaskType taskType, String taskData) {
        try {
            log.info("开始处理任务: {} - {}", taskId, taskType);
            
            // 模拟任务处理时间
            long processingTime = getEstimatedDuration(taskType);
            Thread.sleep(processingTime);
            
            // 生成模拟结果
            String result = generateMockResult(taskType, taskData);
            
            // 更新任务状态
            completeTask(taskId, result);
            
        } catch (Exception e) {
            log.error("任务处理失败: {} - {}", taskId, e.getMessage(), e);
            failTask(taskId, e.getMessage());
        }
    }

    /**
     * 完成任务
     */
    @Transactional
    public void completeTask(String taskId, String result) {
        Task task = taskRepository.findById(taskId).orElse(null);
        if (task != null && task.getStatus() != Task.TaskStatus.CANCELED) {
            task.setStatus(Task.TaskStatus.COMPLETED);
            task.setResult(result);
            taskRepository.save(task);
            log.info("任务完成: {}", taskId);
        }
    }

    /**
     * 任务失败
     */
    @Transactional
    public void failTask(String taskId, String error) {
        Task task = taskRepository.findById(taskId).orElse(null);
        if (task != null && task.getStatus() != Task.TaskStatus.CANCELED) {
            task.setStatus(Task.TaskStatus.FAILED);
            task.setError(error);
            taskRepository.save(task);
        }
    }

    /**
     * 获取预估处理时间
     */
    private long getEstimatedDuration(Task.TaskType taskType) {
        return switch (taskType) {
            case SCRIPT_GENERATION -> 5000; // 5秒
            case SHOT_GENERATION -> 8000; // 8秒
            case VOICE_GENERATION -> 3000; // 3秒
            case VIDEO_GENERATION -> 15000; // 15秒
            case VIDEO_RENDER -> 30000; // 30秒
            case VIDEO_COMPOSE -> 45000; // 45秒
        };
    }

    /**
     * 生成模拟结果
     */
    private String generateMockResult(Task.TaskType taskType, String taskData) {
        return String.format("{\"message\": \"%s completed successfully\", \"data\": \"%s\"}", 
                taskType.name(), taskData != null ? taskData : "");
    }
} 