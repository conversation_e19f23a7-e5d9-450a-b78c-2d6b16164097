package com.example.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * FFmpeg 相关配置
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ffmpeg")
public class FfmpegConfig {

    /**
     * FFmpeg 可执行文件路径
     * 默认为系统PATH中的ffmpeg
     */
    private String executablePath = "ffmpeg";

    /**
     * FFprobe 可执行文件路径
     * 默认为系统PATH中的ffprobe
     */
    private String ffprobePath = "ffprobe";

    /**
     * 视频处理的默认参数
     */
    private VideoDefaults video = new VideoDefaults();

    /**
     * 音频处理的默认参数
     */
    private AudioDefaults audio = new AudioDefaults();

    /**
     * 性能相关配置
     */
    private Performance performance = new Performance();

    @Data
    public static class VideoDefaults {
        /**
         * 默认视频编解码器
         */
        private String codec = "libx264";

        /**
         * 默认像素格式
         */
        private String pixelFormat = "yuv420p";

        /**
         * 默认帧率
         */
        private int frameRate = 25;

        /**
         * 默认分辨率宽度
         */
        private int width = 1920;

        /**
         * 默认分辨率高度
         */
        private int height = 1080;

        /**
         * 默认CRF值（质量控制）
         */
        private int crf = 23;

        /**
         * 默认预设
         */
        private String preset = "medium";

        /**
         * 默认视频时长（秒）
         */
        private int defaultDuration = 5;
    }

    @Data
    public static class AudioDefaults {
        /**
         * 默认音频编解码器
         */
        private String codec = "aac";

        /**
         * 默认音频比特率
         */
        private String bitrate = "128k";

        /**
         * 默认采样率
         */
        private int sampleRate = 44100;

        /**
         * 默认声道数
         */
        private int channels = 2;
    }

    @Data
    public static class Performance {
        /**
         * FFmpeg 并发处理线程数
         * 0表示自动检测
         */
        private int threads = 0;

        /**
         * 是否启用硬件加速
         */
        private boolean hardwareAcceleration = false;

        /**
         * 硬件加速类型 (如: cuda, vaapi, videotoolbox)
         */
        private String hardwareAccelerationType = "auto";

        /**
         * 处理超时时间（秒）
         */
        private int timeoutSeconds = 300;

        /**
         * 最大并发任务数
         */
        private int maxConcurrentTasks = 4;
    }

    /**
     * 画中画位置配置
     */
    @Data
    public static class PictureInPicture {
        /**
         * 叠加视频相对于主视频的大小比例
         */
        private double sizeRatio = 0.25;

        /**
         * 边距（像素）
         */
        private int margin = 20;

        /**
         * 默认位置
         */
        private String defaultPosition = "BOTTOM_RIGHT";
    }

    /**
     * 文件管理配置
     */
    @Data
    public static class FileManagement {
        /**
         * 临时文件目录
         */
        private String tempDirectory = System.getProperty("java.io.tmpdir");

        /**
         * 是否自动清理临时文件
         */
        private boolean autoCleanup = true;

        /**
         * 临时文件保留时间（小时）
         */
        private int tempFileRetentionHours = 24;

        /**
         * 最大文件大小（MB）
         */
        private long maxFileSizeMB = 500;
    }

    private PictureInPicture pip = new PictureInPicture();
    private FileManagement fileManagement = new FileManagement();

    /**
     * 获取完整的FFmpeg命令前缀
     */
    public String[] getFFmpegCommand() {
        return new String[]{executablePath};
    }

    /**
     * 获取完整的FFprobe命令前缀
     */
    public String[] getFFprobeCommand() {
        return new String[]{ffprobePath};
    }

    /**
     * 获取线程数参数
     */
    public String getThreadsArg() {
        return performance.threads > 0 ? String.valueOf(performance.threads) : "auto";
    }

    /**
     * 是否启用硬件加速
     */
    public boolean isHardwareAccelerationEnabled() {
        return performance.hardwareAcceleration;
    }

    /**
     * 获取硬件加速参数
     */
    public String[] getHardwareAccelerationArgs() {
        if (!performance.hardwareAcceleration) {
            return new String[0];
        }

        return switch (performance.hardwareAccelerationType.toLowerCase()) {
            case "cuda" -> new String[]{"-hwaccel", "cuda", "-hwaccel_output_format", "cuda"};
            case "vaapi" -> new String[]{"-hwaccel", "vaapi"};
            case "videotoolbox" -> new String[]{"-hwaccel", "videotoolbox"};
            case "auto" -> new String[]{"-hwaccel", "auto"};
            default -> new String[0];
        };
    }

    /**
     * 获取默认视频编码参数
     */
    public String[] getDefaultVideoEncodingArgs() {
        return new String[]{
                "-c:v", video.codec,
                "-pix_fmt", video.pixelFormat,
                "-r", String.valueOf(video.frameRate),
                "-crf", String.valueOf(video.crf),
                "-preset", video.preset
        };
    }

    /**
     * 获取默认音频编码参数
     */
    public String[] getDefaultAudioEncodingArgs() {
        return new String[]{
                "-c:a", audio.codec,
                "-b:a", audio.bitrate,
                "-ar", String.valueOf(audio.sampleRate),
                "-ac", String.valueOf(audio.channels)
        };
    }
}
