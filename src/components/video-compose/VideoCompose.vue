<template>
  <div>
    <!-- Header -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold text-gray-900 mb-2">
        视频合成
      </h2>
      <p class="text-gray-600">
        AI正在将您的脚本、镜头、配音和数字人合成为完整的视频
      </p>
    </div>

    <!-- Composition Progress View -->
    <div v-if="isComposing" class="space-y-8">
      <!-- Progress Overview -->
      <div class="bg-white rounded-lg p-8 shadow-sm">
        <div class="text-center mb-8">
          <div class="w-20 h-20 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">
            正在合成视频...
          </h3>
          <p class="text-gray-600">
            AI正在处理您的内容，请稍候片刻
          </p>
        </div>

        <!-- Progress Bar -->
        <div class="max-w-lg mx-auto mb-8">
          <div class="flex justify-between text-sm text-gray-600 mb-2">
            <span>合成进度</span>
            <span>{{ compositionProgress }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div
              class="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
              :style="{ width: `${compositionProgress}%` }"
            ></div>
          </div>
        </div>

        <!-- Composition Steps -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
          <div
            v-for="(item, index) in compositionSteps"
            :key="index"
            class="text-center"
          >
            <div 
              class="w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center transition-all duration-300"
              :class="getStepClasses(item, index)"
            >
              <span class="material-icons text-lg">{{ item.icon }}</span>
            </div>
            <p 
              class="text-sm font-medium"
              :class="getStepTextClasses(item, index)"
            >
              {{ item.step }}
            </p>
            <div v-if="item.completed" class="mt-1">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                <span class="material-icons text-xs mr-1">check</span>
                完成
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Real-time Preview -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">实时预览</h3>
          <div class="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <span class="material-icons text-4xl text-gray-400 mb-2 block animate-pulse">
                movie_creation
              </span>
              <p class="text-gray-500 text-sm">合成中...</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">合成详情</h3>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-600">总镜头数</span>
              <span class="font-medium">{{ project?.shots?.length || 0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">预计时长</span>
              <span class="font-medium">2分30秒</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">输出质量</span>
              <span class="font-medium">1080p</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">预计完成</span>
              <span class="font-medium">约{{ Math.ceil((100 - compositionProgress) / 10) }}分钟</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Composition Complete View -->
    <div v-else>
      <div v-if="compositionCompleted" class="space-y-8">
        <!-- Success Banner -->
        <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
          <div class="flex items-center gap-4">
            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
              <span class="material-icons text-white text-2xl">check_circle</span>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">视频合成完成！</h3>
              <p class="text-gray-600">您的视频已成功合成，可以进行预览和调整</p>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Video Preview - Larger -->
          <div class="lg:col-span-2">
            <VideoPreview
              :video-url="composedVideoUrl"
              :is-composed="compositionCompleted"
            />
          </div>

          <!-- Right Sidebar -->
          <div class="lg:col-span-1 space-y-6">
            <!-- Composition Settings -->
            <CompositionSettings :on-recompose="handleRecompose" />
          </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div class="bg-white rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">视频信息</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-600">总时长</span>
                <span class="font-medium">2:30</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">分辨率</span>
                <span class="font-medium">1920×1080</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">帧率</span>
                <span class="font-medium">30fps</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">文件大小</span>
                <span class="font-medium">45.2MB</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">格式</span>
                <span class="font-medium">MP4</span>
              </div>
            </div>
          </div>

          <!-- Timeline -->
          <CompositionTimeline />
        </div>
      </div>

      <!-- Pre-composition View -->
      <div v-else class="text-center bg-white rounded-lg p-12 shadow-sm">
        <div class="max-w-md mx-auto">
          <div class="w-20 h-20 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="material-icons text-4xl text-blue-500">movie_creation</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-4">准备开始视频合成</h3>
          <p class="text-gray-600 mb-8">
            您的所有镜头、配音和数字人都已准备就绪。点击下方按钮，AI将为您制作最终的视频。
          </p>
          <a-button type="primary" size="large" @click="handleStartComposition">
            <span class="material-icons text-base align-middle mr-2">play_circle_filled</span>
            开始合成视频
          </a-button>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div v-if="compositionCompleted && !isComposing" class="flex justify-between items-center mt-8 p-6 bg-white rounded-lg shadow-sm">
      <div>
        <h4 class="font-medium text-gray-900">准备导出视频</h4>
        <p class="text-sm text-gray-600">视频合成已完成，您可以进行最终导出</p>
      </div>
      <div class="flex gap-3">
        <a-button @click="handleRecompose">
          <ReloadOutlined />
          重新合成
        </a-button>
        <a-button type="primary" @click="handleContinue">
          <DownloadOutlined />
          导出视频
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import type { ProjectData } from '../../stores/app'
import { useNavigation } from '../../composables/useNavigation'
import { useApi } from '../../composables/useApi'
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import VideoPreview from './VideoPreview.vue'
import CompositionSettings from './CompositionSettings.vue'
import CompositionTimeline from './CompositionTimeline.vue'

interface CompositionStep {
  step: string
  icon: string
  completed: boolean
}

const route = useRoute()
const { setCurrentTab } = useNavigation()
const { composeVideo, getProject } = useApi()

const isComposing = ref(false)
const compositionProgress = ref(0)
const project = ref<ProjectData | null>(null)
const projectId = computed(() => route.params.projectId as string)
const compositionCompleted = ref(false)
const composedVideoUrl = ref('')

const compositionSteps = reactive<CompositionStep[]>([
  { step: '处理镜头', icon: 'photo_library', completed: false },
  { step: '同步配音', icon: 'record_voice_over', completed: false },
  { step: '添加数字人', icon: 'person', completed: false },
  { step: '最终渲染', icon: 'movie_creation', completed: false }
])

const getStepClasses = (item: CompositionStep, index: number) => {
  if (item.completed) {
    return 'bg-blue-600 text-white shadow-lg'
  } else if (compositionProgress.value > index * 25) {
    return 'bg-blue-100 text-blue-600 animate-pulse'
  } else {
    return 'bg-gray-200 text-gray-500'
  }
}

const getStepTextClasses = (item: CompositionStep, index: number) => {
  if (item.completed) {
    return 'text-blue-600'
  } else if (compositionProgress.value > index * 25) {
    return 'text-blue-600'
  } else {
    return 'text-gray-500'
  }
}

const handleStartComposition = async () => {
  if (!projectId.value) {
    console.error("项目ID未找到，无法开始合成")
    return
  }
  isComposing.value = true
  compositionProgress.value = 0
  
  // 重置步骤状态
  compositionSteps.forEach(step => {
    step.completed = false
  })

  // 调用后端合成接口
  await composeVideo(projectId.value)
  
  // 开始轮询项目状态
  const interval = setInterval(async () => {
    try {
      const updatedProject = await getProject(projectId.value)
      
      // 更新模拟的进度
      if (compositionProgress.value < 90) {
        compositionProgress.value += 10
      }
      compositionSteps.forEach((step, index) => {
        if (compositionProgress.value > (index + 1) * 25) {
          step.completed = true
        }
      })

      if (updatedProject.compositionStatus === 'completed') {
        clearInterval(interval)
        compositionProgress.value = 100
        compositionSteps.forEach(step => step.completed = true)
        
        // 延迟一小段时间再结束，让UI看起来更自然
        setTimeout(() => {
          isComposing.value = false
          composedVideoUrl.value = updatedProject.finalVideoUrl
          compositionCompleted.value = true
        }, 1000)

      } else if (updatedProject.compositionStatus === 'failed') {
        clearInterval(interval)
        isComposing.value = false
        // 可以在这里添加错误处理逻辑
        console.error("视频合成失败")
      }
    } catch (error) {
      clearInterval(interval)
      isComposing.value = false
      console.error("轮询项目状态时出错:", error)
    }
  }, 2000) // 每2秒查询一次
}

const handleRecompose = () => {
  compositionCompleted.value = false
  composedVideoUrl.value = ''
  handleStartComposition()
}

const handleContinue = () => {
  setCurrentTab('export')
}

// Auto-start composition when entering this tab
onMounted(async () => {
  if (projectId.value) {
    project.value = await getProject(projectId.value)
    if (project.value) {
      if (project.value.compositionStatus === 'completed' && project.value.finalVideoUrl) {
        compositionCompleted.value = true
        composedVideoUrl.value = project.value.finalVideoUrl
      }
    }
  }
})
</script>

<style scoped>
.material-icons {
  font-family: 'Material Icons';
}
</style> 