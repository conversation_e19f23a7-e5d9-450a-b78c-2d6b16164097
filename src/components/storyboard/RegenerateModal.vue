<template>
  <a-modal
    :open="isOpen"
    title="重新生成镜头"
    :confirm-loading="isProcessing"
    :width="600"
    @ok="handleConfirm"
    @cancel="handleCancel"
    ok-text="确认生成"
    cancel-text="取消"
  >
    <div class="space-y-4" v-if="shot">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          当前图片
        </label>
        <div class="w-full">
          <img
            v-if="shot.imageUrl"
            :src="shot.imageUrl"
            alt="当前镜头图片"
            class="w-full h-auto rounded-lg border border-gray-200"
          />
          <div v-else class="w-full h-full flex items-center justify-center">
            暂无
          </div>
        </div>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          原始脚本
        </label>
        <div class="bg-gray-50 p-3 rounded-md border border-gray-200 text-sm text-gray-600 max-h-24 overflow-y-auto">
          {{ shot.text }}
        </div>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          输入修改建议 (可选)
        </label>
        <a-textarea
          v-model:value="prompt"
          placeholder="例如：改成卡通风格、让画面更明亮、增加一些科技元素..."
          :rows="3"
          class="resize-vertical"
        />
      </div>

      <div class="flex items-center justify-center w-full my-2">
        <span class="text-sm text-gray-400">或</span>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          自定义上传图片
        </label>
        <a-upload-dragger
            v-model:file-list="fileList"
            name="file"
            :multiple="false"
            :before-upload="() => false"
            @change="handleFileChange"
            @drop="handleFileChange"
        >
          <p class="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            仅支持单张图片上传。如果同时输入修改建议和上传图片，将优先使用上传的图片。
          </p>
        </a-upload-dragger>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Modal as AModal, Upload as AUpload, message, Textarea as ATextarea } from 'ant-design-vue'
import { InboxOutlined } from '@ant-design/icons-vue'

interface Shot {
  id: string
  shotNumber: number
  text: string
  imageUrl?: string
}

interface Props {
  isOpen: boolean
  onClose: () => void
  shot?: Shot | null
  onRegenerate: (shotId: string, options: { prompt?: string, file?: File }) => void
  isProcessing: boolean
}

const props = defineProps<Props>()

const prompt = ref('')
const fileList = ref<any[]>([])
const selectedFile = ref<File | null>(null)

// Watch for modal open/close to reset prompt
watch(() => props.isOpen, (newVal) => {
  if (!newVal) {
    prompt.value = ''
    fileList.value = []
    selectedFile.value = null
  }
})

const handleFileChange = (info: any) => {
  // from antd docs, the file is in info.file
  const file = info.file
  if (file.status === 'removed') {
    selectedFile.value = null
    fileList.value = []
    return
  }

  if (file) {
    //
    selectedFile.value = file.originFileObj || file
    fileList.value = [file]
  }
}

const handleConfirm = () => {
  if (!props.shot) return

  if (!prompt.value && !selectedFile.value) {
    message.warning('请输入修改建议或上传一张图片');
    return;
  }

  AModal.confirm({
    title: '确认更新镜头?',
    content: selectedFile.value
      ? '上传的图片将替换当前镜头图片，确定要继续吗？'
      : '重新生成后当前镜头图片将被替换，确定要继续吗？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      const options: { prompt?: string; file?: File } = {}
      if (selectedFile.value) {
        options.file = selectedFile.value
      } else {
        options.prompt = prompt.value
      }
      props.onRegenerate(props.shot!.id, options)
    }
  })
}

const handleCancel = () => {
  props.onClose()
}
</script>

<style scoped>
.resize-vertical {
  resize: vertical;
}
</style> 