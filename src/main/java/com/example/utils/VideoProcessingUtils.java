package com.example.utils;

import lombok.extern.slf4j.Slf4j;
import net.bramp.ffmpeg.FFmpeg;
import net.bramp.ffmpeg.FFmpegExecutor;
import net.bramp.ffmpeg.FFprobe;
import net.bramp.ffmpeg.builder.FFmpegBuilder;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 视频处理工具类，扩展 FfmpegUtils 的功能
 * 提供更高级的视频处理操作
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
public final class VideoProcessingUtils {

    private VideoProcessingUtils() {
        // 防止实例化
    }

    /**
     * 标准化视频文件，确保所有视频具有相同的编解码器参数
     * 这对于FFmpeg的concat demuxer非常重要
     *
     * @param inputPath  输入视频文件路径
     * @param outputPath 输出视频文件路径
     * @throws IOException 如果FFmpeg执行出错
     */
    public static void standardizeVideo(String inputPath, String outputPath) throws IOException {
        FFmpeg ffmpeg = new FFmpeg();
        FFprobe ffprobe = new FFprobe();

        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(inputPath)
                .addOutput(outputPath)
                .setVideoCodec("libx264")
                .setAudioCodec("aac")
                .setVideoPixelFormat("yuv420p")
                .setVideoFrameRate(25) // 标准化帧率
                .setVideoResolution(1920, 1080) // 标准化分辨率
                .addExtraArgs("-preset", "medium")
                .addExtraArgs("-crf", "23")
                .done();

        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
        
        log.info("视频标准化完成: {} -> {}", inputPath, outputPath);
    }

    /**
     * 批量标准化视频文件
     *
     * @param inputPaths  输入视频文件路径列表
     * @param outputDir   输出目录
     * @return 标准化后的视频文件路径列表
     * @throws IOException 如果处理过程中发生错误
     */
    public static List<String> batchStandardizeVideos(List<String> inputPaths, String outputDir) throws IOException {
        // 确保输出目录存在
        Path outputDirPath = Paths.get(outputDir);
        if (!Files.exists(outputDirPath)) {
            Files.createDirectories(outputDirPath);
        }

        return inputPaths.stream()
                .map(inputPath -> {
                    try {
                        String fileName = Paths.get(inputPath).getFileName().toString();
                        String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
                        String outputPath = outputDir + "/" + "std_" + nameWithoutExt + ".mp4";
                        
                        standardizeVideo(inputPath, outputPath);
                        return outputPath;
                    } catch (IOException e) {
                        log.error("标准化视频失败: {}", inputPath, e);
                        throw new RuntimeException("视频标准化失败: " + inputPath, e);
                    }
                })
                .toList();
    }

    /**
     * 调整视频音量
     *
     * @param inputPath  输入视频文件路径
     * @param outputPath 输出视频文件路径
     * @param volume     音量倍数（1.0为原音量，2.0为两倍音量，0.5为一半音量）
     * @throws IOException 如果FFmpeg执行出错
     */
    public static void adjustVideoVolume(String inputPath, String outputPath, double volume) throws IOException {
        FFmpeg ffmpeg = new FFmpeg();
        FFprobe ffprobe = new FFprobe();

        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(inputPath)
                .addOutput(outputPath)
                .setVideoCodec("copy") // 不重编码视频
                .setAudioCodec("aac")
                .addExtraArgs("-af", "volume=" + volume)
                .done();

        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
        
        log.info("视频音量调整完成: {} -> {}, 音量倍数: {}", inputPath, outputPath, volume);
    }

    /**
     * 添加淡入淡出效果
     *
     * @param inputPath     输入视频文件路径
     * @param outputPath    输出视频文件路径
     * @param fadeInDuration  淡入时长（秒）
     * @param fadeOutDuration 淡出时长（秒）
     * @throws IOException 如果FFmpeg执行出错
     */
    public static void addFadeEffect(String inputPath, String outputPath, 
                                   double fadeInDuration, double fadeOutDuration) throws IOException {
        FFmpeg ffmpeg = new FFmpeg();
        FFprobe ffprobe = new FFprobe();

        // 构建视频滤镜
        String videoFilter = String.format("fade=in:0:%d,fade=out:st=%d:d=%d", 
                (int)(fadeInDuration * 25), // 假设25fps
                (int)((fadeOutDuration) * 25), 
                (int)(fadeOutDuration * 25));

        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(inputPath)
                .addOutput(outputPath)
                .setVideoCodec("libx264")
                .setAudioCodec("copy")
                .addExtraArgs("-vf", videoFilter)
                .done();

        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
        
        log.info("淡入淡出效果添加完成: {} -> {}", inputPath, outputPath);
    }

    /**
     * 创建视频缩略图
     *
     * @param videoPath     视频文件路径
     * @param thumbnailPath 缩略图输出路径
     * @param timeOffset    截取时间点（秒）
     * @throws IOException 如果FFmpeg执行出错
     */
    public static void createThumbnail(String videoPath, String thumbnailPath, double timeOffset) throws IOException {
        FFmpeg ffmpeg = new FFmpeg();
        FFprobe ffprobe = new FFprobe();

        FFmpegBuilder builder = new FFmpegBuilder()
                .setInput(videoPath)
                .addOutput(thumbnailPath)
                .setFrames(1)
                .addExtraArgs("-ss", String.valueOf(timeOffset))
                .addExtraArgs("-vf", "scale=320:180") // 缩略图尺寸
                .done();

        FFmpegExecutor executor = new FFmpegExecutor(ffmpeg, ffprobe);
        executor.createJob(builder).run();
        
        log.info("视频缩略图创建完成: {} -> {}", videoPath, thumbnailPath);
    }

    /**
     * 清理临时文件
     *
     * @param filePaths 要清理的文件路径列表
     */
    public static void cleanupFiles(List<String> filePaths) {
        for (String filePath : filePaths) {
            try {
                Files.deleteIfExists(Paths.get(filePath));
                log.debug("清理文件: {}", filePath);
            } catch (IOException e) {
                log.warn("清理文件失败: {}", filePath, e);
            }
        }
    }

    /**
     * 检查视频文件是否存在且有效
     *
     * @param videoPath 视频文件路径
     * @return 如果文件存在且大小大于0则返回true
     */
    public static boolean isValidVideoFile(String videoPath) {
        try {
            Path path = Paths.get(videoPath);
            return Files.exists(path) && Files.size(path) > 0;
        } catch (IOException e) {
            log.warn("检查视频文件失败: {}", videoPath, e);
            return false;
        }
    }
}
