package com.example.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 脚本镜头实体
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "t_script_shot")
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class ScriptShot {

    @Id
    @Column(length = 50)
    private String id;

    @Column(name = "project_id", nullable = false, length = 50)
    private String projectId;

    @Column(columnDefinition = "TEXT")
    private String text;

    @Column(name = "order_index")
    private Integer orderIndex;

    @Column(name = "shot_number")
    private Integer shotNumber;

    @Column(name = "image_url", length = 500)
    private String imageUrl;

    @Column(name = "image_path", length = 500)
    private String imagePath;

    @Column(name = "audio_url", length = 500)
    private String audioUrl;

    @Column(name = "audio_path", length = 500)
    private String audioPath;

    @Column(name = "video_url", length = 500)
    private String videoUrl;

    @Column(name = "video_path", length = 500)
    private String videoPath;

    @Column(name = "avatar_id", length = 50)
    private String avatarId;

    @Column(name = "avatar_url", length = 500)
    private String avatarUrl;

    @Column(name = "avatar_path", length = 500)
    private String avatarPath;

    @Column(name = "final_video_path", length = 500)
    private String finalVideoPath;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 关联项目
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    private Project project;
} 