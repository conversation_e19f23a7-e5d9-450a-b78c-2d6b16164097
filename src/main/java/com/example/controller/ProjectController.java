package com.example.controller;

import com.example.common.response.Result;
import com.example.param.asset.AssetRecordVO;
import com.example.param.project.ProjectCreateParam;
import com.example.param.project.ProjectRecordVO;
import com.example.param.project.ProjectUpdateParam;
import com.example.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 项目管理控制器
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/projects")
@Tag(name = "项目管理", description = "项目相关的API接口")
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectService projectService;

    @Operation(summary = "创建项目", description = "创建新的视频项目")
    @PostMapping
    public Result<ProjectRecordVO> createProject(@Valid @RequestBody ProjectCreateParam param) {
        log.info("创建项目请求: {}", param.getName());
        ProjectRecordVO result = projectService.createProject(param);
        return Result.success(result);
    }

    @Operation(summary = "获取项目列表", description = "获取所有项目列表")
    @GetMapping
    public Result<List<ProjectRecordVO>> getAllProjects(
            @Parameter(description = "项目名称（模糊查询）") @RequestParam(required = false) String name,
            @Parameter(description = "是否草稿") @RequestParam(required = false) Boolean isDraft) {
        log.debug("获取项目列表请求: name={}, isDraft={}", name, isDraft);
        
        List<ProjectRecordVO> result;
        if (name != null || isDraft != null) {
            result = projectService.findProjectsByConditions(name, isDraft);
        } else {
            result = projectService.getAllProjects();
        }
        return Result.success(result);
    }

    @Operation(summary = "获取项目详情", description = "根据ID获取项目详细信息")
    @GetMapping("/{projectId}")
    public Result<ProjectRecordVO> getProjectById(
            @Parameter(description = "项目ID") @PathVariable String projectId) {
        log.debug("获取项目详情请求: {}", projectId);
        ProjectRecordVO result = projectService.getProjectById(projectId);
        return Result.success(result);
    }

    @Operation(summary = "更新项目", description = "更新项目信息")
    @PutMapping("/{projectId}")
    public Result<ProjectRecordVO> updateProject(
            @Parameter(description = "项目ID") @PathVariable String projectId,
            @Valid @RequestBody ProjectUpdateParam param) {
        log.info("更新项目请求: {}", projectId);
        ProjectRecordVO result = projectService.updateProject(projectId, param);
        return Result.success(result);
    }

    @Operation(summary = "删除项目", description = "删除指定项目")
    @DeleteMapping("/{projectId}")
    public Result<Void> deleteProject(
            @Parameter(description = "项目ID") @PathVariable String projectId) {
        log.info("删除项目请求: {}", projectId);
        projectService.deleteProject(projectId);
        return Result.success();
    }

    @PostMapping("/{projectId}/assets/upload")
    public Result<AssetRecordVO> uploadAsset(
        @PathVariable String projectId,
        @RequestParam("file") MultipartFile file) {
        log.info("上传项目素材: projectId={}, file={}", projectId, file.getOriginalFilename());
        AssetRecordVO asset = projectService.uploadAsset(projectId, file);
        return Result.success(asset);
    }

    @DeleteMapping("/assets/{assetId}")
    public Result<Void> deleteAsset(
        @PathVariable String assetId) {
        log.info("删除项目素材: assetId={}", assetId);
        projectService.deleteAsset(assetId);
        return Result.success();
    }

    @GetMapping("/{projectId}/assets")
    public Result<List<AssetRecordVO>> getProjectAssets(
        @PathVariable String projectId) {
        log.info("获取项目素材: projectId={}", projectId);
        List<AssetRecordVO> assets = projectService.getProjectAssets(projectId);
        return Result.success(assets);
    }

    @Operation(summary = "合成项目视频", description = "为指定项目启动视频合成任务")
    @PostMapping("/{projectId}/compose")
    public Result<Void> composeProject(
            @Parameter(description = "项目ID") @PathVariable String projectId) {
        log.info("合成项目视频请求: projectId={}", projectId);
        projectService.composeProject(projectId);
        return Result.success();
    }
} 