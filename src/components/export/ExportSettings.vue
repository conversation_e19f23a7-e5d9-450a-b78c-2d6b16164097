<template>
  <div class="bg-white rounded-lg p-6 shadow-sm">
    <h3 class="text-lg font-semibold text-gray-900 mb-6">导出设置</h3>
    
    <div class="space-y-4">
      <!-- Video Quality -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          视频质量
        </label>
        <a-select
          :value="appStore.exportSettings.quality"
          @change="(value: string) => handleSettingChange('quality', value)"
          class="w-full"
        >
          <a-select-option value="1080p">1080p</a-select-option>
          <a-select-option value="720p">720p</a-select-option>
          <a-select-option value="480p">480p</a-select-option>
        </a-select>
      </div>

      <!-- File Format -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          文件格式
        </label>
        <a-select
          :value="appStore.exportSettings.format"
          @change="(value: string) => handleSettingChange('format', value)"
          class="w-full"
        >
          <a-select-option value="MP4">MP4</a-select-option>
          <a-select-option value="MOV">MOV</a-select-option>
          <a-select-option value="AVI">AVI</a-select-option>
        </a-select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '../../stores/app'

const appStore = useAppStore()

const handleSettingChange = (setting: string, value: string) => {
  appStore.updateExportSettings({ [setting]: value })
}
</script> 