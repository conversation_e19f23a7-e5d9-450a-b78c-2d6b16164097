package com.example.repository;

import com.example.entity.Asset;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 资产Repository接口
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Repository
public interface AssetRepository extends JpaRepository<Asset, String>, JpaSpecificationExecutor<Asset> {

    /**
     * 根据项目ID查询资产
     */
    List<Asset> findByProjectIdOrderByUploadedAtDesc(String projectId);

    /**
     * 根据项目ID删除资产
     */
    void deleteByProjectId(String projectId);

    /**
     * 根据文件名查询资产
     */
    List<Asset> findByFilename(String filename);
} 