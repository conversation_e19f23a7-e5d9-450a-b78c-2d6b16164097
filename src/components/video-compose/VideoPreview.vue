<template>
  <div class="bg-white rounded-lg p-6 shadow-sm">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">视频预览</h3>
      <div class="flex gap-2">
        <a-button size="small" @click="handleFullscreen">
          <FullscreenOutlined />
        </a-button>
        <a-button size="small" @click="handleDownload">
          <DownloadOutlined />
        </a-button>
      </div>
    </div>

    <div class="aspect-video bg-gray-100 rounded-lg overflow-hidden">
      <video
        v-if="isComposed && videoUrl"
        :src="videoUrl"
        controls
        class="w-full h-full object-cover"
        preload="metadata"
      >
        您的浏览器不支持视频播放
      </video>
      
      <div v-else class="w-full h-full flex items-center justify-center">
        <div class="text-center">
          <span class="material-icons text-4xl text-gray-400 mb-2 block">
            movie_creation
          </span>
          <p class="text-gray-500">视频预览将在合成完成后显示</p>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { FullscreenOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

interface Props {
  videoUrl?: string
  isComposed: boolean
}

const props = defineProps<Props>()

const handleFullscreen = () => {
  console.log('全屏播放')
}

const handleDownload = () => {
  if (props.videoUrl) {
    const link = document.createElement('a')
    link.href = props.videoUrl
    link.download = 'composed-video.mp4'
    link.click()
  } else {
    message.warning('视频尚未合成完成')
  }
}

const handleShare = () => {
  console.log('分享视频')
  message.success('分享链接已复制到剪贴板')
}
</script>

<style scoped>
.material-icons {
  font-family: 'Material Icons';
}
</style> 