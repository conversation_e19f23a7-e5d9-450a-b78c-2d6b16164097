package com.example.controller;

import com.example.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 资源管理控制器
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/resources")
@Tag(name = "资源管理", description = "头像、语音等资源相关的API接口")
@RequiredArgsConstructor
public class ResourceController {

    @Value("${app.api-base-url}")
    private String apiBaseUrl;

    @Value("${app.static-path}")
    private String staticPath;

    @Operation(summary = "获取头像列表", description = "获取所有可用的头像资源")
    @GetMapping("/avatars")
    public Result<List<Map<String, Object>>> getAvatars() {
        log.debug("获取头像列表请求");
        
        List<Map<String, Object>> avatars = List.of(
            Map.of(
                "id", "emma",
                "name", "艾玛",
                "gender", "female",
                "style", "professional",
                "description", "专业的商务女性形象",
                "previewUrl", apiBaseUrl + "/static/female1-512.jpeg",
                "filePath", staticPath + "/female1-512.jpeg"
            ),
            Map.of(
                "id", "david",
                "name", "大卫",
                "gender", "male",
                "style", "professional",
                "description", "成熟稳重的商务男性形象",
                "previewUrl", apiBaseUrl + "/static/male1-512.jpeg",
                "filePath", staticPath + "/male1-512.jpeg"
            ),
            Map.of(
                "id", "sophia",
                "name", "索菲亚",
                "gender", "female",
                "style", "casual",
                "description", "年轻活泼的女性形象",
                "previewUrl", apiBaseUrl + "/static/female2-512.jpeg",
                "filePath", staticPath + "/female2-512.jpeg"
            ),
            Map.of(
                "id", "alex",
                "name", "亚历克斯",
                "gender", "male",
                "style", "creative",
                "description", "富有创意的年轻男性形象",
                "previewUrl", apiBaseUrl + "/static/male2-512.jpeg",
                "filePath", staticPath + "/male2-512.jpeg"
            )
        );
        
        return Result.success(avatars);
    }

    @Operation(summary = "获取语音列表", description = "获取所有可用的语音资源")
    @GetMapping("/voices")
    public Result<List<Map<String, Object>>> getVoices() {
        log.debug("获取语音列表请求");
        
        List<Map<String, Object>> voices = List.of(
            Map.of(
                "id", "professional-female",
                "name", "专业女声",
                "lang", "zh-CN",
                "gender", "female",
                "style", "professional",
                "description", "清晰自然的中文女性声音，适合商务和教育内容",
                "icon", "👩‍💼",
                "characteristics", List.of("清晰", "专业", "自然"),
                "previewUrl", apiBaseUrl + "/static/zf_xiaoxiao.wav"
            ),
            Map.of(
                "id", "energetic-male",
                "name", "活力男声",
                "lang", "zh-CN",
                "gender", "male",
                "style", "energetic",
                "description", "充满能量的中文男性声音，适合营销和宣传内容",
                "icon", "👨‍💼",
                "characteristics", List.of("活力", "热情", "感染力"),
                "previewUrl", apiBaseUrl + "/static/zm_yunxi.wav"
            ),
            Map.of(
                "id", "gentle-child",
                "name", "温柔童声",
                "lang", "zh-CN",
                "gender", "child",
                "style", "gentle",
                "description", "温柔甜美的中文女声，适合儿童和家庭内容",
                "icon", "👶",
                "characteristics", List.of("可爱", "温柔", "亲和"),
                "previewUrl", apiBaseUrl + "/static/zm_yunyang.wav"
            ),
            Map.of(
                "id", "news-anchor",
                "name", "新闻主播",
                "lang", "zh-CN",
                "gender", "male",
                "style", "news",
                "description", "权威正式的中文播报风格，适合新闻和正式场合",
                "icon", "📺",
                "characteristics", List.of("权威", "正式", "稳重"),
                "previewUrl", apiBaseUrl + "/static/zf_xiaoyi.wav"
            )
        );
        
        return Result.success(voices);
    }
} 