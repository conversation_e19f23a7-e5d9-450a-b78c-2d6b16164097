<template>
  <div>
    <div class="mb-8">
      <h2 class="text-2xl font-semibold text-gray-900 mb-2">
        镜头编辑
      </h2>
      <p class="text-gray-600">
        AI已根据您的脚本生成了以下镜头，您可以对每个镜头的图片进行调整或重新生成
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && projectStatus !== 'completed'" class="flex justify-center p-8">
      <LoadingSpinner text="AI镜头生成中，请稍候..." />
    </div>

    <!-- Failed State -->
    <div v-else-if="projectStatus === 'failed'" class="text-center py-12 text-red-500">
      镜头生成失败，请返回上一步重试。
    </div>

    <!-- Empty State -->
    <div v-else-if="shots.length === 0" class="text-center py-12 text-gray-500">
      未找到任何镜头。
    </div>

    <!-- Success State -->
    <div v-else>
      <div class="flex flex-col gap-6 mb-8">
        <ShotCard
          v-for="(shot, index) in shots"
          :key="shot.id"
          :index="index"
          :shot="shot"
          :on-regenerate="handleRegenerateShot"
          :is-processing="isRegenerating"
        />
      </div>
      <div class="flex justify-end">
        <a-button
          type="primary"
          :disabled="shots.length === 0"
          @click="handleConfirmShots"
        >
          确认镜头并进入配音
        </a-button>
      </div>
    </div>

    <!-- Regenerate Modal -->
    <RegenerateModal
      :is-open="isModalOpen"
      :on-close="closeModal"
      :shot="modalData"
      :on-regenerate="handleConfirmRegenerate"
      :is-processing="isRegenerating"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { useAppStore } from '../../stores/app'
import { useNavigation } from '../../composables/useNavigation'
import { useApi } from '../../composables/useApi'
import { useInterval } from '../../composables/useInterval'
import LoadingSpinner from '../common/LoadingSpinner.vue'
import ShotCard from './ShotCard.vue'
import RegenerateModal from './RegenerateModal.vue'

interface Shot {
  id: string
  shotNumber: number
  text: string
  imageUrl?: string
}

interface Project {
  id: string
  shotGenerationStatus: 'not_started' | 'in_progress' | 'completed' | 'failed'
}

const appStore = useAppStore()
const { setCurrentTab } = useNavigation()
const { getProject, getShots, regenerateShot, triggerShotGeneration, uploadShotImage } = useApi()

const shots = ref<Shot[]>([])
const projectStatus = ref<'not_started' | 'in_progress' | 'completed' | 'failed'>('not_started')
const isLoading = ref(true)
const isRegenerating = ref(false)

// Modal state
const isModalOpen = ref(false)
const modalData = ref<Shot | null>(null)

// Polling setup
const pollingDelay = ref<number | null>(null)
useInterval(() => {
  fetchProjectAndShots()
}, pollingDelay)

const fetchProjectAndShots = async () => {
  if (!appStore.projectId) return

  try {
    const project = await getProject(appStore.projectId) as Project
    const status = project.shotGenerationStatus
    projectStatus.value = status

    if (status === 'completed') {
      pollingDelay.value = null
      const shotsData = await getShots(appStore.projectId)
      shots.value = shotsData || []
      isLoading.value = false
    } else if (status === 'failed') {
      pollingDelay.value = null
      isLoading.value = false
      message.error('镜头生成失败，请返回上一步重试。')
    } else if (status === 'not_started') {
      isLoading.value = true
      try {
        await triggerShotGeneration(appStore.projectId)
        message.success("已开始为您生成镜头...")
        pollingDelay.value = 4000 // Start polling
      } catch (err) {
        console.error("Failed to trigger shot generation:", err)
        message.error("无法开始生成镜头，请稍后重试。")
        pollingDelay.value = null
        isLoading.value = false
      }
    } else { // in_progress
      pollingDelay.value = 4000 // Start or continue polling
      isLoading.value = true // Keep loading state while polling
    }
  } catch (error) {
    console.error("Failed to fetch project status or shots:", error)
    pollingDelay.value = null
    isLoading.value = false
    message.error("无法获取项目状态，请刷新页面。")
  }
}

const handleRegenerateShot = (shot: Shot) => {
  modalData.value = shot
  isModalOpen.value = true
}

const closeModal = () => {
  modalData.value = null
  isModalOpen.value = false
}

const handleConfirmRegenerate = async (shotId: string, options: { prompt?: string; file?: File }) => {
  if (!shotId) return

  isRegenerating.value = true
  closeModal()

  try {
    let updatedShot;
    if (options.file) {
      // Custom upload path
      updatedShot = await uploadShotImage(shotId, options.file)
      message.success("镜头图片已成功替换")
    } else {
      // AI regeneration path
      updatedShot = await regenerateShot(shotId, { prompt: options.prompt || '' })
      message.success("镜头已重新生成")
    }
    shots.value = shots.value.map(s => s.id === shotId ? updatedShot : s)
  } catch (error) {
    console.error("Failed to update shot:", error)
    message.error("镜头更新失败")
  } finally {
    isRegenerating.value = false
  }
}

const handleConfirmShots = () => {
  // 检查是否有未生成成功的镜头
  const failedShot = shots.value.find(shot => !shot.imageUrl)
  
  if (failedShot) {
    message.error(`部分镜头生成失败，请重新生成。`)
    return
  }

  setCurrentTab('voice')
}

// Lifecycle
onMounted(() => {
  fetchProjectAndShots()
})

onUnmounted(() => {
  pollingDelay.value = null
})
</script>

<style scoped>
/* Custom styles if needed */
</style> 