<template>
  <div>
    <!-- Header -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold text-gray-900 mb-2">
        导出视频
      </h2>
      <p class="text-gray-600">
        您的视频已生成完成，可以预览并导出
      </p>
    </div>

    <div class="bg-white rounded-lg p-8 shadow-sm">
      <!-- Final Preview -->
      <div class="mb-8">
        <video
          controls
          poster="https://images.unsplash.com/photo-1517649763942-7e3c76255010?w=600&h=338&fit=crop&q=80"
          class="w-full rounded-lg"
        >
          <source src="" type="video/mp4" />
          您的浏览器不支持视频播放。
        </video>
      </div>

      <!-- Export Settings -->
      <div class="mb-8">
        <ExportSettings />
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end gap-4">
        <a-button
          @click="handleSaveProject"
        >
          保存项目
        </a-button>
        <a-button
          type="primary"
          @click="handleDownloadVideo"
        >
          下载视频
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import ExportSettings from './ExportSettings.vue'

const handleSaveProject = () => {
  console.log('Saving project...')
  // Implement save project logic
  message.success('项目已保存')
}

const handleDownloadVideo = () => {
  console.log('Downloading video...')
  // Implement download logic
  message.success('视频下载已开始')
}
</script> 