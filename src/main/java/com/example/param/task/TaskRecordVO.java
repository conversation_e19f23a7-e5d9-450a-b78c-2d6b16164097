package com.example.param.task;

import com.example.entity.Task;

import java.time.LocalDateTime;

/**
 * 任务Record VO
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
public record TaskRecordVO(
        String id,
        String projectId,
        String type,
        String status,
        String result,
        String error,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
) {
    public static TaskRecordVO from(Task task) {
        return new TaskRecordVO(
            task.getId(),
            task.getProjectId(),
            task.getType().name(),
            task.getStatus().name(),
            task.getResult(),
            task.getError(),
            task.getCreatedAt(),
            task.getUpdatedAt()
        );
    }
}