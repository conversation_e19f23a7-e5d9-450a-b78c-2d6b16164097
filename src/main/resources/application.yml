# Spring Boot 应用配置 - 公共配置
spring:
  application:
    name: video-backend-java
  threads:
    virtual:
      enabled: true
  
  # 环境配置
  profiles:
    active: dev
  
  # 数据源公共配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.sqlite.JDBC
    hikari:
      connection-test-query: SELECT 1
      max-lifetime: 1800000
      connection-timeout: 30000
      idle-timeout: 600000
  
  # JPA公共配置
  jpa:
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.community.dialect.SQLiteDialect
        format_sql: true
    database-platform: org.hibernate.community.dialect.SQLiteDialect
  
  # Jackson序列化配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    default-property-inclusion: NON_NULL

# 服务器公共配置
server:
  port: 9800
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# 应用自定义配置
app:
  api-base-url: ${API_BASE_URL:http://localhost:9800}
  upload-path: ${UPLOAD_PATH:/Users/<USER>/video-project/uploads}
  static-path: ${STATIC_PATH:/Users/<USER>/video-project/static}

# Springdoc OpenAPI UI Configuration
springdoc:
  api-info:
    title: Video Backend API
    version: 0.0.1-SNAPSHOT
    description: API documentation for Video Backend Java
    terms-of-service: urn:tos # Replace with actual ToS URL
    license:
      name: Apache 2.0
      url: https://www.apache.org/licenses/LICENSE-2.0.html
  # Swagger UI Path
  swagger-ui:
    path: /swagger-ui.html # Default path for Swagger UI
    enabled: false       # Disable Swagger UI by default
  # API Docs Path
  api-docs:
    path: /v3/api-docs # Default path for OpenAPI spec (JSON)
    enabled: false       # Disable API docs by default
