<template>
  <div class="flex flex-col h-full">
    <!-- Logo -->
    <div :class="['p-5 mb-8 flex items-center', isCollapsed ? 'justify-center' : 'gap-3']">
      <img
        src="https://images.unsplash.com/photo-1501854140801-50d01698950b?w=150&h=100&fit=crop&q=80"
        alt="AI视频生成平台"
        class="w-8 h-8 rounded flex-shrink-0"
      />
      <h1 v-if="!isCollapsed" class="text-lg font-semibold whitespace-nowrap">AI视频生成平台</h1>
    </div>

    <!-- Navigation Links -->
    <div class="flex flex-col gap-1 flex-1">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        @click="handleTabClick(tab.id)"
        :disabled="!canNavigateToTab(tab.id)"
        :class="[
          'flex items-center gap-3 px-5 py-3 text-left transition-all duration-300',
          currentTab === tab.id
            ? 'bg-gray-800 text-white border-l-4 border-blue-500 cursor-pointer'
            : canNavigateToTab(tab.id)
              ? 'text-gray-300 hover:bg-gray-800 hover:text-white cursor-pointer'
              : 'text-gray-600 cursor-not-allowed opacity-50 pointer-events-none',
          isCollapsed ? 'justify-center px-3' : ''
        ]"
        :title="isCollapsed ? tab.label : ''"
      >
        <span class="material-icons text-xl flex-shrink-0">{{ tab.icon }}</span>
        <span v-if="!isCollapsed" class="font-medium whitespace-nowrap">{{ tab.label }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useNavigation } from '../../composables/useNavigation'

interface Props {
  isCollapsed?: boolean
}

withDefaults(defineProps<Props>(), {
  isCollapsed: false
})

const { tabs, currentTab, setCurrentTab, canNavigateToTab } = useNavigation()

const handleTabClick = (tabId: string) => {
  if (canNavigateToTab(tabId)) {
    setCurrentTab(tabId)
  }
}
</script> 