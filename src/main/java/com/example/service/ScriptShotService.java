package com.example.service;

import com.example.common.exception.BusinessException;
import com.example.common.response.ResultCode;
import com.example.entity.Asset;
import com.example.entity.Project;
import com.example.entity.ScriptShot;
import com.example.param.script.GenerateVideoParam;
import com.example.param.script.GenerateVoiceParam;
import com.example.param.script.ScriptShotRecordVO;
import com.example.param.script.ScriptUpdateParam;
import com.example.repository.AssetRepository;
import com.example.repository.ProjectRepository;
import com.example.repository.ScriptShotRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 脚本镜头服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScriptShotService {

    @Value("${app.upload-path}")
    private String uploadPath;

    @Value("${app.static-path}")
    private String staticPath;

    @Value("${app.api-base-url}")
    private String apiBaseUrl;

    private final ScriptShotRepository scriptShotRepository;
    private final ProjectRepository projectRepository;
    private final AssetRepository assetRepository;
    private final ShotGenerationService shotGenerationService;

    /**
     * 生成脚本
     */
    @Transactional
    public void generateScripts(String projectId) {
        log.info("开始生成脚本: {}", projectId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));

        // 检查并更新脚本生成状态 - 只有not_started或failed状态才能开始生成
        Project.GenerationStatus currentStatus = project.getScriptGenerationStatus();
        if (currentStatus != Project.GenerationStatus.not_started && currentStatus != Project.GenerationStatus.failed) {
            if (currentStatus == Project.GenerationStatus.in_progress) {
                throw new BusinessException(ResultCode.OPERATION_FAILED, "脚本生成中，请稍后");
            } else {
                throw new BusinessException(ResultCode.OPERATION_FAILED, "当前项目已生成脚本");
            }
        }

        // 更新状态为生成中
        project.setScriptGenerationStatus(Project.GenerationStatus.in_progress);
        projectRepository.save(project);

        log.info("项目 {} 脚本生成状态已设置为 'in_progress'", projectId);

        // 异步生成脚本
        processScriptGenerationAsync(projectId);
    }

    /**
     * 获取项目脚本
     */
    public List<ScriptShotRecordVO> getProjectScripts(String projectId) {
        log.debug("获取项目脚本: {}", projectId);

        List<ScriptShot> scripts = scriptShotRepository.findByProjectIdOrderByOrderIndexAsc(projectId);
        return scripts.stream()
                .map(ScriptShotRecordVO::from)
                .toList();
    }

    /**
     * 更新脚本
     */
    @Transactional
    public ScriptShotRecordVO updateScript(String scriptId, ScriptUpdateParam param) {
        log.info("更新脚本: {}", scriptId);

        ScriptShot script = scriptShotRepository.findById(scriptId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "脚本不存在"));

        if (StringUtils.hasText(param.getText())) {
            script.setText(param.getText());
        }
        if (param.getOrderIndex() != null) {
            script.setOrderIndex(param.getOrderIndex());
            script.setShotNumber(param.getOrderIndex() + 1);
        }

        script = scriptShotRepository.save(script);

        log.info("脚本更新成功: {}", scriptId);
        return ScriptShotRecordVO.from(script);
    }

    /**
     * 添加脚本
     */
    @Transactional
    public ScriptShotRecordVO addScript(String projectId, String text, Integer orderIndex) {
        log.info("添加脚本: projectId={}, orderIndex={}", projectId, orderIndex);

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "项目不存在");
        }

        // 如果orderIndex为空，查询最大值并设置为最大值+1
        if (orderIndex == null) {
            Integer maxOrderIndex = scriptShotRepository.findMaxOrderIndexByProjectId(projectId);
            Integer maxShotNumber = scriptShotRepository.findMaxShotNumberByProjectId(projectId);

            orderIndex = (maxOrderIndex != null ? maxOrderIndex : -1) + 1;
            log.info("orderIndex为空，查询到最大orderIndex={}, 最大shotNumber={}, 设置新orderIndex={}",
                    maxOrderIndex, maxShotNumber, orderIndex);
        }

        ScriptShot script = new ScriptShot();
        script.setUpdatedAt(LocalDateTime.now());
        script.setCreatedAt(LocalDateTime.now());
        script.setId("script_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16));
        script.setProjectId(projectId);
        script.setText(text);
        script.setOrderIndex(orderIndex);
        script.setShotNumber(orderIndex + 1);

        script = scriptShotRepository.save(script);

        log.info("脚本添加成功: {}", script.getId());
        return ScriptShotRecordVO.from(script);
    }

    /**
     * 在指定位置插入脚本
     */
    @Transactional
    public ScriptShotRecordVO insertScript(String projectId, String text, Integer insertIndex) {
        log.info("在指定位置插入脚本: projectId={}, insertIndex={}", projectId, insertIndex);

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "项目不存在");
        }

        // 如果insertIndex为空，添加到最后
        if (insertIndex == null) {
            return addScript(projectId, text, null);
        }

        // 将插入位置及之后的所有脚本的orderIndex都+1
        List<ScriptShot> existingScripts = scriptShotRepository.findByProjectIdOrderByOrderIndexAsc(projectId);
        for (ScriptShot existingScript : existingScripts) {
            if (existingScript.getOrderIndex() >= insertIndex) {
                existingScript.setOrderIndex(existingScript.getOrderIndex() + 1);
                existingScript.setShotNumber(existingScript.getOrderIndex() + 1);
                existingScript.setUpdatedAt(LocalDateTime.now());
            }
        }
        scriptShotRepository.saveAll(existingScripts);

        // 创建新脚本
        ScriptShot script = new ScriptShot();
        script.setUpdatedAt(LocalDateTime.now());
        script.setCreatedAt(LocalDateTime.now());
        script.setId("script_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16));
        script.setProjectId(projectId);
        script.setText(text);
        script.setOrderIndex(insertIndex);
        script.setShotNumber(insertIndex + 1);

        script = scriptShotRepository.save(script);

        log.info("脚本插入成功: {} at position {}", script.getId(), insertIndex);
        return ScriptShotRecordVO.from(script);
    }

    /**
     * 重新排序脚本
     */
    @Transactional
    public List<ScriptShotRecordVO> reorderScripts(String projectId, List<String> scriptIds) {
        log.info("重新排序脚本: projectId={}, scriptIds={}", projectId, scriptIds);

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "项目不存在");
        }

        // 获取所有脚本
        List<ScriptShot> scripts = scriptShotRepository.findByProjectIdOrderByOrderIndexAsc(projectId);

        // 验证脚本ID列表是否完整
        if (scripts.size() != scriptIds.size()) {
            throw new BusinessException(ResultCode.OPERATION_FAILED, "脚本ID列表不完整");
        }

        // 创建ID到脚本的映射
        var scriptMap = scripts.stream()
                .collect(Collectors.toMap(ScriptShot::getId, script -> script));

        // 按新顺序重新设置orderIndex和shotNumber
        for (int i = 0; i < scriptIds.size(); i++) {
            String scriptId = scriptIds.get(i);
            ScriptShot script = scriptMap.get(scriptId);
            if (script == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "脚本不存在: " + scriptId);
            }

            script.setOrderIndex(i);
            script.setShotNumber(i + 1);
            script.setUpdatedAt(LocalDateTime.now());
        }

        // 保存所有脚本
        List<ScriptShot> updatedScripts = scriptShotRepository.saveAll(scripts);

        log.info("脚本重新排序完成: projectId={}", projectId);
        return updatedScripts.stream()
                .sorted((a, b) -> Integer.compare(a.getOrderIndex(), b.getOrderIndex()))
                .map(ScriptShotRecordVO::from)
                .toList();
    }

    /**
     * 删除脚本
     */
    @Transactional
    public void deleteScript(String scriptId) {
        log.info("删除脚本: {}", scriptId);

        if (!scriptShotRepository.existsById(scriptId)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "脚本不存在");
        }

        scriptShotRepository.deleteById(scriptId);
        log.info("脚本删除成功: {}", scriptId);
    }

    /**
     * 重新生成脚本
     */
    @Transactional
    public ScriptShotRecordVO regenerateScript(String scriptId) {
        log.info("重新生成脚本: {}", scriptId);

        ScriptShot script = scriptShotRepository.findById(scriptId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "脚本不存在"));

        // 模拟AI重新生成脚本内容
        String regeneratedText = "重新生成的脚本内容：" + script.getText() + "（已优化）";
        script.setText(regeneratedText);

        script = scriptShotRepository.save(script);

        log.info("脚本重新生成成功: {}", scriptId);
        return ScriptShotRecordVO.from(script);
    }

    /**
     * 异步处理脚本生成
     */
    @Async
    @Transactional
    public void processScriptGenerationAsync(String projectId) {
        processScriptGeneration(projectId);
    }

    /**
     * 处理脚本生成
     */
    @Transactional
    private void processScriptGeneration(String projectId) {
        try {
            log.info("开始后台脚本生成处理: {}", projectId);

            // 获取项目数据
            Project project = projectRepository.findById(projectId).orElse(null);
            if (project == null) {
                throw new RuntimeException("项目 " + projectId + " 在后台任务中未找到");
            }

            // 获取资源文件
            List<Asset> assets = assetRepository.findByProjectIdOrderByUploadedAtDesc(projectId);

            // 确定素材来源
            boolean hasTextMaterial = StringUtils.hasText(project.getTextMaterial()) &&
                    !project.getTextMaterial().trim().isEmpty();
            String materialSource = hasTextMaterial ? "text" : "upload";

            log.info("项目 {} 素材来源: {}", projectId, materialSource);

            // 模拟脚本生成时间（2秒）
            Thread.sleep(2000);

            // 清除现有脚本
            scriptShotRepository.deleteByProjectId(projectId);

            // 生成模拟脚本
            List<String> generatedTexts = generateMockScripts(project, assets, materialSource, hasTextMaterial);

            for (int i = 0; i < generatedTexts.size(); i++) {
                ScriptShot script = new ScriptShot();
                script.setId("script_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16));
                script.setProjectId(projectId);
                script.setText(generatedTexts.get(i));
                script.setOrderIndex(i);
                script.setShotNumber(i + 1);
                script.setUpdatedAt(LocalDateTime.now());
                script.setCreatedAt(LocalDateTime.now());
                scriptShotRepository.save(script);
            }

            // 更新项目状态
            project.setScriptGenerationStatus(Project.GenerationStatus.completed);
            projectRepository.save(project);

            log.info("✅ 项目 {} 脚本生成状态已设置为 'completed'", projectId);

        } catch (Exception e) {
            log.error("项目 {} 后台脚本生成失败: {}", projectId, e.getMessage(), e);

            // 更新失败状态
            Project project = projectRepository.findById(projectId).orElse(null);
            if (project != null) {
                project.setScriptGenerationStatus(Project.GenerationStatus.failed);
                projectRepository.save(project);
            }
        }
    }

    /**
     * 生成模拟脚本
     */
    private List<String> generateMockScripts(Project project, List<Asset> assets, String materialSource, boolean hasTextMaterial) {
        // 构建素材信息
        String materialInfo;
        if (hasTextMaterial) {
            String textPreview = project.getTextMaterial().substring(0, Math.min(20, project.getTextMaterial().length())) + "...";
            materialInfo = textPreview;
        } else {
            materialInfo = assets.stream()
                    .map(Asset::getOriginalName)
                    .collect(Collectors.joining(", "));
        }

        return List.of(
                String.format("根据素材 (%s)：%s，我们构思了第一个场景。", materialSource, materialInfo),
                "第二个场景将深入探讨核心价值，展现产品的独特魅力。",
                "高潮部分，用户的使用体验和成功案例将集中展示。",
                "最后，以一个强有力的号召性用语结束，鼓励观众采取行动。",
                "这是一个由AI生成的结尾彩蛋脚本。"
        );
    }


    /**
     * 触发镜头生成
     */
    @Transactional
    public void triggerShotGeneration(String projectId) {
        log.info("开始生成镜头: {}", projectId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));

        // 检查并更新镜头生成状态 - 只有not_started或failed状态才能开始生成
        Project.GenerationStatus currentStatus = project.getShotGenerationStatus();
        if (currentStatus != Project.GenerationStatus.not_started && currentStatus != Project.GenerationStatus.failed) {
            if (currentStatus == Project.GenerationStatus.in_progress) {
                throw new BusinessException(ResultCode.OPERATION_FAILED, "镜头生成中，请稍后");
            } else {
                throw new BusinessException(ResultCode.OPERATION_FAILED, "当前项目已生成镜头");
            }
        }

        // 更新状态为生成中
        project.setShotGenerationStatus(Project.GenerationStatus.in_progress);
        projectRepository.save(project);

        log.info("项目 {} 镜头生成状态已设置为 'in_progress'", projectId);

        // 异步生成镜头
        shotGenerationService.processShotGenerationAsync(projectId);
    }

    /**
     * 获取项目镜头
     * @param projectId
     * @return
     */
    public List<ScriptShotRecordVO> getShotsByProjectId(String projectId) {
        List<ScriptShot> shots = scriptShotRepository.findByProjectIdOrderByOrderIndexAsc(projectId);
        return shots.stream()
                .map(ScriptShotRecordVO::from)
                .toList();
    }

    /**
     * 重新生成镜头
     * @param shotId 镜头ID
     * @param prompt 提示词(可选)
     * @return 更新后的镜头信息
     */
    @Transactional
    public ScriptShotRecordVO regenerateShot(String shotId, String prompt) {
        log.info("重新生成镜头: {}", shotId);
        if (prompt != null && !prompt.isEmpty()) {
            log.info("使用提示词重新生成镜头 {}: \"{}\"", shotId, prompt);
        } else {
            log.info("无提示词重新生成镜头 {}", shotId);
        }

        ScriptShot shot = scriptShotRepository.findById(shotId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "镜头不存在"));

        // 模拟AI重新生成镜头图片
        String newImageUrl = String.format("https://picsum.photos/seed/%s/400/225", UUID.randomUUID().toString().substring(0, 5));
        shot.setImageUrl(newImageUrl);
        // todo fix
        shot.setImagePath("");
        shot.setUpdatedAt(LocalDateTime.now());

        shot = scriptShotRepository.save(shot);

        return ScriptShotRecordVO.from(shot);
    }

    /**
     * 为镜头上传自定义图片
     * @param shotId 镜头ID
     * @param file 上传的文件
     * @return 更新后的镜头信息
     */
    @Transactional
    public ScriptShotRecordVO uploadShotImage(String shotId, MultipartFile file) {
        log.info("为镜头 {} 上传自定义图片: {}", shotId, file.getOriginalFilename());

        ScriptShot shot = scriptShotRepository.findById(shotId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "镜头不存在"));

        String projectId = shot.getProjectId();

        try {
            // 1. 保存文件
            String uploadDir = String.join(File.separator, uploadPath, projectId, "shots");
            
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            String extension = StringUtils.getFilenameExtension(file.getOriginalFilename());
            String newFileName = "shot_" + shot.getId() + "_" + UUID.randomUUID().toString().substring(0, 8) + (extension != null ? "." + extension : "");
            File dest = new File(dir, newFileName);
            

            file.transferTo(dest);

            // 2. 更新数据库记录 - 遵循ProjectService中的模式
            // 这里的URL构建方式比较特殊，假设服务器有相应的配置来处理它
            String fileUrl = String.join(File.separator, apiBaseUrl, "uploads", projectId, "shots", newFileName);

            shot.setImageUrl(fileUrl);
            shot.setImagePath(dest.getAbsolutePath());
            shot.setUpdatedAt(LocalDateTime.now());
            ScriptShot updatedShot = scriptShotRepository.save(shot);

            log.info("镜头 {} 图片更新成功. 新URL: {}", shotId, fileUrl);
            return ScriptShotRecordVO.from(updatedShot);

        } catch (IOException e) {
            log.error("为镜头 {} 上传图片失败: {}", shotId, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "文件上传失败");
        }
    }

    /**
     * 生成配音
     */
    @Transactional
    public ScriptShotRecordVO generateVoice(GenerateVoiceParam param) {
        sleep(3);
        log.info("生成配音: shotId={}, voiceType={}", param.getId(), param.getVoiceType());

        ScriptShot shot = scriptShotRepository.findById(param.getId())
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "镜头不存在"));

        // 可选的配音音频文件列表
        List<String> voiceUrls = List.of(
                "http://localhost:9800/static/zf_xiaoxiao.wav",
                "http://localhost:9800/static/zm_yunxi.wav",
                "http://localhost:9800/static/zm_yunyang.wav",
                "http://localhost:9800/static/zf_xiaoyi.wav"
        );

        // 随机选择一个音频文件
        String selectedAudioUrl = voiceUrls.get((int) (Math.random() * voiceUrls.size()));

        // 更新镜头的音频URL
        shot.setAudioUrl(selectedAudioUrl);
        shot.setAudioPath(staticPath + shot.getAudioUrl().split("/")[(shot.getAudioUrl().split("/").length - 1)]);
        shot.setUpdatedAt(LocalDateTime.now());

        // 保存更新
        shot = scriptShotRepository.save(shot);

        log.info("配音生成成功: shotId={}, audioUrl={}", param.getId(), selectedAudioUrl);

        return ScriptShotRecordVO.from(shot);
    }

    public ScriptShotRecordVO generateVideo(GenerateVideoParam param) {
        sleep(3);

        log.info("生成视频: {}", param.shotId());
        ScriptShot shot = scriptShotRepository.findById(param.shotId())
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "镜头不存在"));


        List<String> strings = List.of(
                "http://127.0.0.1:9800/static/8762656-uhd_3840_2160_25fps.mp4",
                "http://127.0.0.1:9800/static/output.mp4"
        );
        shot.setVideoUrl(strings.get((int) (Math.random() * strings.size())));
        shot.setVideoPath(staticPath + "/" + shot.getVideoUrl().split("/")[(shot.getVideoUrl().split("/").length - 1)]);
        shot.setFinalVideoPath(staticPath + "/" + shot.getVideoUrl().split("/")[(shot.getVideoUrl().split("/").length - 1)]);

        shot.setUpdatedAt(LocalDateTime.now());
        shot = scriptShotRepository.save(shot);

        return ScriptShotRecordVO.from(shot);
    }

    /**
     * 为镜头生成数字人视频
     * @param shotId 镜头ID
     * @param avatarId 数字人ID
     * @return 更新后的镜头信息
     */
    @Transactional
    public ScriptShotRecordVO generateAvatarVideo(String shotId, String avatarId) {
        log.info("为镜头 {} 生成数字人视频, avatarId: {}", shotId, avatarId);

        if (!StringUtils.hasText(avatarId)) {
            throw new BusinessException(ResultCode.VALIDATION_ERROR, "必须提供数字人ID");
        }
        
        ScriptShot shot = scriptShotRepository.findById(shotId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "镜头不存在"));

        // 模拟调用AI服务进行视频合成
        // 在真实场景中，这里会调用一个Python服务（如Wav2Lip）
        // 它需要：shot.getImagePath(), shot.getAudioPath(), avatar.getFilePath()
        // 此处我们仅模拟成功，并返回一个预设的视频URL

        log.info("模拟AI视频生成中... ShotId: {}", shotId);
        sleep(5); // 模拟耗时

        List<String> videoUrls = List.of(
                "http://127.0.0.1:9800/static/8762656-uhd_3840_2160_25fps.mp4",
                "http://127.0.0.1:9800/static/output.mp4"
        );
        String selectedVideoUrl = videoUrls.get((int) (Math.random() * videoUrls.size()));
        String videoFileName = selectedVideoUrl.substring(selectedVideoUrl.lastIndexOf("/") + 1);
        
        shot.setVideoUrl(selectedVideoUrl);
        shot.setVideoPath(staticPath + File.separator + videoFileName);
        shot.setFinalVideoPath(shot.getVideoPath());
        shot.setAvatarId(avatarId);
        shot.setUpdatedAt(LocalDateTime.now());

        ScriptShot updatedShot = scriptShotRepository.save(shot);
        log.info("镜头 {} 的数字人视频生成成功. URL: {}", shotId, selectedVideoUrl);

        return ScriptShotRecordVO.from(updatedShot);
    }

    @Transactional
    public ScriptShotRecordVO clearAvatar(String shotId) {
        log.info("清除镜头 {} 的数字人配置", shotId);
        
        ScriptShot shot = scriptShotRepository.findById(shotId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "镜头不存在"));

        shot.setAvatarId(null);
        shot.setAvatarUrl(null);
        shot.setAvatarPath(null);
        shot.setFinalVideoPath(shot.getVideoPath());
        shot.setUpdatedAt(LocalDateTime.now());

        ScriptShot updatedShot = scriptShotRepository.save(shot);
        log.info("镜头 {} 的数字人配置已清除", shotId);

        return ScriptShotRecordVO.from(updatedShot);
    }

    private void sleep(long seconds) {
        try {
            Thread.sleep(seconds * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}