<template>
  <div class="api-status">
    <a-badge
      :status="status"
      :color="statusColor"
      :text="statusText"
      class="cursor-pointer"
      @click="checkApiStatus"
      title="点击重新检查 API 状态"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useApi } from '../../composables/useApi'

const { checkApiHealth } = useApi()

const connected = ref(false)
const loading = ref(false)
const lastChecked = ref<Date | null>(null)

const status = computed(() => {
  if (loading.value) return 'processing'
  return connected.value ? 'success' : 'error'
})

const statusColor = computed(() => {
  if (loading.value) return 'blue'
  return connected.value ? 'green' : 'red'
})

const statusText = computed(() => {
  if (loading.value) return '检查连接中...'
  if (connected.value) {
    const timeStr = lastChecked.value ? ` (${lastChecked.value.toLocaleTimeString()})` : ''
    return `API 已连接${timeStr}`
  }
  return 'API 连接失败 (点击重试)'
})

// 检查 API 状态
const checkApiStatus = async () => {
  if (loading.value) return

  loading.value = true
  
  try {
    // 使用健康检查方法
    await checkApiHealth()
    connected.value = true
    lastChecked.value = new Date()
  } catch (error) {
    console.warn('API 健康检查失败:', error)
    connected.value = false
    lastChecked.value = new Date()
  } finally {
    loading.value = false
  }
}

// 组件挂载时检查 API 状态
onMounted(() => {
  checkApiStatus()
})
</script>

<style scoped>
.api-status {
  display: inline-flex;
  align-items: center;
}
</style> 