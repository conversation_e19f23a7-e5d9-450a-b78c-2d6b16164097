package com.example.controller;

import com.example.common.response.Result;
import com.example.param.task.TaskCreateParam;
import com.example.param.task.TaskRecordVO;
import com.example.service.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务管理控制器
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/tasks")
@Tag(name = "任务管理", description = "异步任务相关的API接口")
@RequiredArgsConstructor
public class TaskController {

    private final TaskService taskService;

    @Operation(summary = "创建任务", description = "创建新的异步任务")
    @PostMapping
    public Result<TaskRecordVO> createTask(@Valid @RequestBody TaskCreateParam param) {
        log.info("创建任务请求: {} - {}", param.getTaskType(), param.getProjectId());
        TaskRecordVO result = taskService.createTask(param);
        return Result.success(result);
    }

    @Operation(summary = "获取任务详情", description = "根据ID获取任务详细信息")
    @GetMapping("/{taskId}")
    public Result<TaskRecordVO> getTaskById(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        log.debug("获取任务详情请求: {}", taskId);
        TaskRecordVO result = taskService.getTaskById(taskId);
        return Result.success(result);
    }

    @Operation(summary = "获取项目任务列表", description = "获取指定项目的任务列表")
    @GetMapping("/project/{projectId}")
    public Result<List<TaskRecordVO>> getProjectTasks(
            @Parameter(description = "项目ID") @PathVariable String projectId) {
        log.debug("获取项目任务列表请求: {}", projectId);
        List<TaskRecordVO> result = taskService.getProjectTasks(projectId);
        return Result.success(result);
    }

    @Operation(summary = "取消任务", description = "取消指定的任务")
    @PostMapping("/{taskId}/cancel")
    public Result<Void> cancelTask(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        log.info("取消任务请求: {}", taskId);
        taskService.cancelTask(taskId);
        return Result.success();
    }
}
 