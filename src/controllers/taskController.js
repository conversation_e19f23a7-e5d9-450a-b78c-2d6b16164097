const { nanoid } = require('nanoid');
const { getDB } = require('../db/database');

// In-memory store for task processing simulation
const runningTasks = {};

const createTask = (req, res) => {
    const db = getDB();
    const { projectId, taskType = 'video-render', taskData = {} } = req.body;
    const taskId = `task_${nanoid()}`;
    const now = new Date().toISOString();

    try {
        if (projectId) {
            const project = db.prepare('SELECT id FROM projects WHERE id = ?').get(projectId);
            if (!project) return res.status(404).json({ message: 'Project not found' });
        }
        
        const stmt = db.prepare(
            `INSERT INTO tasks (id, project_id, type, status, created_at, updated_at)
             VALUES (@id, @project_id, @type, @status, @created_at, @updated_at)`
        );
        stmt.run({
            id: taskId,
            project_id: projectId,
            type: taskType,
            status: 'processing',
            created_at: now,
            updated_at: now,
        });

        const estimatedDuration = getEstimatedDuration(taskType, taskData);
        processTask(taskId, taskType, taskData, estimatedDuration);

        res.status(202).json({ taskId, estimatedDuration, status: 'processing' });
    } catch (error) {
        console.error('Failed to create task:', error);
        res.status(500).json({ message: 'Failed to create task in database' });
    }
};

const getTaskById = (req, res) => {
    const db = getDB();
    const { taskId } = req.params;
    try {
        const task = db.prepare('SELECT * FROM tasks WHERE id = ?').get(taskId);
        if (task) {
            if(task.result) task.result = JSON.parse(task.result);
            res.json({ success: true, task });
        } else {
            res.status(404).json({ message: 'Task not found' });
        }
    } catch (error) {
        res.status(500).json({ message: 'Failed to retrieve task' });
    }
};

const getProjectTasks = (req, res) => {
    const db = getDB();
    const { projectId } = req.params;
    try {
        const tasks = db.prepare('SELECT * FROM tasks WHERE project_id = ? ORDER BY created_at DESC').all(projectId);
        tasks.forEach(t => { if(t.result) t.result = JSON.parse(t.result); });
        res.json({ success: true, tasks, count: tasks.length });
    } catch (error) {
        res.status(500).json({ message: 'Failed to retrieve project tasks' });
    }
};

const cancelTask = (req, res) => {
    const db = getDB();
    const { taskId } = req.params;
    
    if (runningTasks[taskId]) {
        clearInterval(runningTasks[taskId]);
        delete runningTasks[taskId];
    }
    
    try {
        const info = db.prepare(`UPDATE tasks SET status = 'canceled', updated_at = ? WHERE id = ?`)
                       .run(new Date().toISOString(), taskId);
        
        if (info.changes > 0) {
            res.json({ success: true, message: 'Task canceled successfully' });
        } else {
            res.status(404).json({ message: 'Task not found' });
        }
    } catch (error) {
        res.status(500).json({ message: 'Failed to cancel task' });
    }
};

// --- Task Simulation Logic (mostly unchanged) ---

const processTask = (taskId, taskType, taskData, estimatedDuration) => {
    const updateInterval = Math.max(500, estimatedDuration / 20);
    let progress = 0;

    const intervalId = setInterval(() => {
        progress += 5;
        if (progress >= 100) {
            clearInterval(intervalId);
            delete runningTasks[taskId];
            completeTask(taskId, taskType, taskData);
        }
    }, updateInterval);
    
    runningTasks[taskId] = intervalId;
};

const completeTask = (taskId, taskType, taskData) => {
    const db = getDB();
    const result = generateMockResult(taskType, taskData);
    
    try {
        db.prepare(`UPDATE tasks SET status = 'completed', result = ?, updated_at = ? WHERE id = ?`)
          .run(JSON.stringify(result), new Date().toISOString(), taskId);
        console.log(`Task ${taskId} (${taskType}) completed and saved to DB.`);
    } catch (error) {
        console.error(`Failed to complete task ${taskId} in DB:`, error);
    }
};

const getEstimatedDuration = (taskType, taskData) => {
  switch (taskType) {
    case 'script-generation': return 5000;
    case 'voice-generation': return (taskData.shots?.length || 1) * 3000;
    default: return 10000;
  }
};

const generateMockResult = (taskType, taskData) => {
    // This can be expanded
    return { message: `${taskType} completed successfully` };
};

module.exports = {
  createTask,
  getTaskById,
  getProjectTasks,
  cancelTask,
};