package com.example.controller;

import com.example.common.response.Result;
import com.example.param.script.GenerateVideoParam;
import com.example.param.script.GenerateVoiceParam;
import com.example.param.script.RegenerateShotParam;
import com.example.param.script.ScriptAddParam;
import com.example.param.script.ScriptInsertParam;
import com.example.param.script.ScriptReorderParam;
import com.example.param.script.ScriptShotRecordVO;
import com.example.param.script.ScriptUpdateParam;
import com.example.service.ScriptShotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 脚本管理控制器
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/scripts")
@Tag(name = "脚本管理", description = "脚本和镜头相关的API接口")
@RequiredArgsConstructor
public class ScriptController {

    private final ScriptShotService scriptShotService;

    @Operation(summary = "添加脚本", description = "添加新的脚本")
    @PostMapping
    public Result<ScriptShotRecordVO> addScript(@RequestBody ScriptAddParam param) {
        log.info("添加脚本请求: {}", param.getProjectId());
        ScriptShotRecordVO result = scriptShotService.addScript(param.getProjectId(), param.getText(), param.getOrderIndex());
        return Result.success(result);
    }

    @Operation(summary = "插入脚本", description = "在指定位置插入新的脚本")
    @PostMapping("/insert")
    public Result<ScriptShotRecordVO> insertScript(@Valid @RequestBody ScriptInsertParam param) {
        log.info("插入脚本请求: projectId={}, insertIndex={}", param.getProjectId(), param.getInsertIndex());
        ScriptShotRecordVO result = scriptShotService.insertScript(param.getProjectId(), param.getText(), param.getInsertIndex());
        return Result.success(result);
    }

    @Operation(summary = "重新排序脚本", description = "重新排序项目中的脚本")
    @PostMapping("/reorder")
    public Result<List<ScriptShotRecordVO>> reorderScripts(@Valid @RequestBody ScriptReorderParam param) {
        log.info("重新排序脚本请求: projectId={}", param.getProjectId());
        List<ScriptShotRecordVO> result = scriptShotService.reorderScripts(param.getProjectId(), param.getScriptIds());
        return Result.success(result);
    }


    @Operation(summary = "生成脚本", description = "为指定项目生成脚本")
    @PostMapping("/generate/{projectId}")
    public Result<Void> generateScripts(@PathVariable String projectId) {
        log.info("生成脚本请求: {}", projectId);
        scriptShotService.generateScripts(projectId);
        return Result.success();
    }

    @Operation(summary = "获取项目脚本", description = "获取指定项目的脚本列表")
    @GetMapping("/project/{projectId}")
    public Result<List<ScriptShotRecordVO>> getProjectScripts(
            @Parameter(description = "项目ID") @PathVariable String projectId) {
        log.debug("获取项目脚本请求: {}", projectId);
        List<ScriptShotRecordVO> result = scriptShotService.getProjectScripts(projectId);
        return Result.success(result);
    }

    @Operation(summary = "更新脚本", description = "更新指定脚本的内容")
    @PutMapping("/{scriptId}")
    public Result<ScriptShotRecordVO> updateScript(
            @Parameter(description = "脚本ID") @PathVariable String scriptId,
            @Valid @RequestBody ScriptUpdateParam param) {
        log.info("更新脚本请求: {}", scriptId);
        ScriptShotRecordVO result = scriptShotService.updateScript(scriptId, param);
        return Result.success(result);
    }

    @Operation(summary = "删除脚本", description = "删除指定的脚本")
    @DeleteMapping("/{scriptId}")
    public Result<Void> deleteScript(
            @Parameter(description = "脚本ID") @PathVariable String scriptId) {
        log.info("删除脚本请求: {}", scriptId);
        scriptShotService.deleteScript(scriptId);
        return Result.success();
    }

    @Operation(summary = "重新生成脚本", description = "重新生成指定的脚本")
    @PostMapping("/{scriptId}/regenerate")
    public Result<ScriptShotRecordVO> regenerateScript(
            @Parameter(description = "脚本ID") @PathVariable String scriptId) {
        log.info("重新生成脚本请求: {}", scriptId);
        ScriptShotRecordVO result = scriptShotService.regenerateScript(scriptId);
        return Result.success(result);
    }

    @Operation(summary = "生成脚本镜头", description = "根据脚本生成镜头")
    @PostMapping("/project/{projectId}/generate-shots")
    public Result<Void> triggerShotGeneration(
            @Parameter(description = "项目ID") @PathVariable String projectId) {
        log.info("生成脚本镜头请求: {}", projectId);
        scriptShotService.triggerShotGeneration(projectId);
        return Result.success();
    }

    @Operation(summary = "获取项目镜头", description = "获取指定项目的镜头列表")
    @GetMapping("/project/{projectId}/shots")
    public Result<List<ScriptShotRecordVO>> getShotsByProjectId(
            @Parameter(description = "项目ID") @PathVariable String projectId) {
        log.debug("获取项目镜头请求: {}", projectId);
        List<ScriptShotRecordVO> result = scriptShotService.getShotsByProjectId(projectId);
        return Result.success(result);
    }

    @Operation(summary = "重新生成镜头", description = "重新生成指定的镜头")
    @PostMapping("/shots/{shotId}/regenerate")
    public Result<ScriptShotRecordVO> regenerateShot(
            @Parameter(description = "镜头ID") @PathVariable String shotId, @RequestBody RegenerateShotParam param) {
        log.info("重新生成镜头请求: {}", shotId);
        ScriptShotRecordVO result = scriptShotService.regenerateShot(shotId, param.getPrompt());
        return Result.success(result);
    }

    @Operation(summary = "为镜头生成数字人视频", description = "为指定的镜头生成数字人视频")
    @PostMapping("/shots/{shotId}/generate-avatar")
    public Result<ScriptShotRecordVO> generateAvatarVideo(
            @Parameter(description = "镜头ID") @PathVariable String shotId,
            @RequestBody Map<String, String> payload) {
        log.info("为镜头生成数字人视频请求: shotId={}", shotId);
        String avatarId = payload.get("avatarId");
        ScriptShotRecordVO result = scriptShotService.generateAvatarVideo(shotId, avatarId);
        return Result.success(result);
    }

    @Operation(summary = "清除镜头数字人配置", description = "清除指定镜头的数字人视频和配置")
    @PostMapping("/shots/{shotId}/clear-avatar")
    public Result<ScriptShotRecordVO> clearAvatar(
            @Parameter(description = "镜头ID") @PathVariable String shotId) {
        log.info("清除镜头数字人配置请求: shotId={}", shotId);
        ScriptShotRecordVO result = scriptShotService.clearAvatar(shotId);
        return Result.success(result);
    }

    @Operation(summary = "为镜头自定义上传图片", description = "为指定的镜头上传自定义图片")
    @PostMapping("/shots/{shotId}/upload-image")
    public Result<ScriptShotRecordVO> uploadShotImage(
            @Parameter(description = "镜头ID") @PathVariable String shotId,
            @RequestParam("file") MultipartFile file) {
        log.info("为镜头上传自定义图片请求: shotId={}", shotId);
        ScriptShotRecordVO result = scriptShotService.uploadShotImage(shotId, file);
        return Result.success(result);
    }

    @Operation(summary = "生成镜头配音", description = "生成指定的镜头配音")
    @PostMapping("/generate-voice")
    public Result<ScriptShotRecordVO> generateVoice(
            @RequestBody GenerateVoiceParam param) {
        log.info("生成镜头配音请求: {}", param.getId());
        ScriptShotRecordVO result = scriptShotService.generateVoice(param);
        return Result.success(result);
    }


    @Operation(summary = "生成视频", description = "生成视频")
    @PostMapping("/generate-video")
    public Result<ScriptShotRecordVO> generateVideo(
            @RequestBody GenerateVideoParam param) {
        log.info("生成视频请求: {}", param.shotId());
        ScriptShotRecordVO result = scriptShotService.generateVideo(param);
        return Result.success(result);
    }


} 