<template>
  <div class="bg-white border-b border-gray-200 px-4 md:px-6 lg:px-8 py-3 md:py-4">
    <!-- 桌面端布局 -->
    <div class="hidden md:flex justify-between items-center">
      <!-- Progress Steps -->
      <div class="flex-1 mr-4"></div>

      <!-- User Info -->
      <div class="flex items-center gap-2 lg:gap-4 flex-shrink-0">
        <ApiStatus />
        <span class="text-sm text-gray-500 hidden lg:inline max-w-32 truncate">
          {{ appStore.projectName }}
        </span>
        <!-- <Button
          variant="secondary"
          size="small"
          @click="handleSaveProject"
        >
          保存
        </Button> -->
        <a-button
          type="primary"
          size="small"
          @click="handleExitProject"
        >
          返回
        </a-button>
      </div>
    </div>

    <!-- 移动端布局 -->
    <div class="md:hidden">
      <!-- 顶部行：项目名称和操作按钮 -->
      <div class="flex justify-between items-center mb-3">
        <div class="flex items-center gap-2 flex-1 min-w-0">
          <ApiStatus />
          <span class="text-sm text-gray-500 truncate">
            {{ appStore.projectName }}
          </span>
        </div>

        <div class="flex items-center gap-2 flex-shrink-0">
          <!-- <Button
            variant="secondary"
            size="small"
            @click="handleSaveProject"
          >
            保存
          </Button> -->
          <a-button
            type="primary"
            @click="handleExitProject"
          >
            返回
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAppStore } from '../../stores/app'
import ApiStatus from '../common/ApiStatus.vue'

const router = useRouter()
const appStore = useAppStore()

const handleExitProject = () => {
  router.push('/')
}
</script> 