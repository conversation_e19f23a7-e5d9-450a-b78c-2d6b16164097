package com.example.param.script;

import lombok.Data;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 脚本重新排序参数
 */
@Data
public class ScriptReorderParam {

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private String projectId;

    /**
     * 脚本ID列表，按新的顺序排列
     */
    @NotEmpty(message = "脚本ID列表不能为空")
    private List<String> scriptIds;
} 