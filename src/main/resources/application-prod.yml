# 生产环境配置 - 仅环境特定配置
spring:
  # 生产环境数据源配置
  datasource:
    url: jdbc:sqlite:${DB_PATH:/Users/<USER>/video-project/database.sqlite}
    username: # SQLite不需要用户名
    password: # SQLite不需要密码
    hikari:
      minimum-idle: 1
      maximum-pool-size: 1
  
  # 生产环境JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  
  # 生产环境Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}

# 生产环境服务器配置
server:
  port: ${SERVER_PORT:8080}

# 生产环境日志配置
logging:
  level:
    root: WARN
    com.example: INFO
  file:
    name: /var/log/java-project-template/application.log
    max-size: 100MB
    max-history: 30
