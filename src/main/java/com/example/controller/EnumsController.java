package com.example.controller;


import com.example.common.enums.BaseEnum;
import com.example.common.option.Option;
import com.example.common.response.Result;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.CaseFormat;
import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/enums")
public class EnumsController {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Enums {
        private Map<String, Map<String, String>> map = new HashMap<>();
        private Map<String, List<Option>> list = new HashMap<>();
    }

    private static final Enums ENUMS = new Enums();

    private final ObjectMapper objectMapper;

    @PostConstruct
    public void init() throws JsonProcessingException {
        // Scan enums package and get all enums
        Reflections reflections = new Reflections("com.example");
        Set<Class<? extends BaseEnum>> enumClasses = reflections.getSubTypesOf(BaseEnum.class);

        Map<String, Map<String, String>> allMap = enumClasses.stream()
                .collect(Collectors.toMap(
                        enumClass -> CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_CAMEL, enumClass.getSimpleName()),
                        this::getEnumMap
                ));

        Map<String, List<Option>> allList = enumClasses.stream()
                .collect(Collectors.toMap(
                        enumClass -> CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_CAMEL, enumClass.getSimpleName()),
                        this::getEnumOptions
                ));

        ENUMS.setMap(allMap);
        ENUMS.setList(allList);
        log.info("EnumsController init, enums: {}", objectMapper.writeValueAsString(ENUMS));

    }

    private Map<String, String> getEnumMap(Class<? extends BaseEnum> enumClass) {
        Map<String, String> map = new HashMap<>();
        for (BaseEnum enumConstant : enumClass.getEnumConstants()) {
            map.put(enumConstant.getName(), enumConstant.getLabel());
        }
        return map;
    }

    private List<Option> getEnumOptions(Class<? extends BaseEnum> enumClass) {
        List<Option> list = new ArrayList<>();
        for (BaseEnum enumConstant : enumClass.getEnumConstants()) {
            list.add(new Option(enumConstant.getLabel(), enumConstant.getName()));
        }
        return list;
    }


    @GetMapping
    public Result<Enums> all() {
        return Result.success(ENUMS);
    }
}
