package com.example.entity;

import com.example.common.enums.BaseEnum;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 任务实体
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "t_task")
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class Task {

    @Id
    @Column(length = 50)
    private String id;

    @Column(name = "project_id", length = 50)
    private String projectId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 30)
    private TaskType type;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private TaskStatus status = TaskStatus.PENDING;

    @Column(columnDefinition = "TEXT")
    private String result;

    @Column(columnDefinition = "TEXT")
    private String error;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 关联项目
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    private Project project;

    /**
     * 任务类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum TaskType implements BaseEnum {
        SCRIPT_GENERATION("脚本生成"),
        SHOT_GENERATION("镜头生成"),
        VOICE_GENERATION("语音生成"),
        VIDEO_GENERATION("视频生成"),
        VIDEO_RENDER("视频渲染"),
        VIDEO_COMPOSE("视频合成");

        private final String label;
    }

    /**
     * 任务状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum TaskStatus implements BaseEnum {
        PENDING("等待中"),
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        CANCELED("已取消");

        private final String label;
    }
} 