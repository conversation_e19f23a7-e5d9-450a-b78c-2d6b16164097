package com.example.param.asset;

import com.example.entity.Asset;

import java.time.LocalDateTime;

/**
 * 资产Record VO
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
public record AssetRecordVO(
        String id,
        String projectId,
        String originalName,
        String filename,
        Long size,
        String url,
        LocalDateTime uploadedAt
) {
    public static AssetRecordVO from(Asset asset) {
        return new AssetRecordVO(
            asset.getId(),
            asset.getProjectId(),
            asset.getOriginalName(),
            asset.getFilename(),
            asset.getSize(),
            asset.getUrl(),
            asset.getUploadedAt()
        );
    }
} 