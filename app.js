require('dotenv').config();

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const multer = require('multer');
const { initializeDatabase } = require('./src/db/database');

// Import routes
const projectRoutes = require('./src/routes/projectRoutes');
const taskRoutes = require('./src/routes/taskRoutes');
const resourceRoutes = require('./src/routes/resourceRoutes');
const scriptRoutes = require('./src/routes/scriptRoutes');
const voiceRoutes = require('./src/routes/voiceRoutes');
const videoRoutes = require('./src/routes/videoRoutes');
const avatarRoutes = require('./src/routes/avatarRoutes');

const app = express();
const port = process.env.PORT || 3001;

// --- Middlewares ---
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use(express.static(path.join(__dirname, 'public')));

// --- API Routes ---
app.use('/api/projects', projectRoutes);
app.use('/api/resources', resourceRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/scripts', scriptRoutes);
app.use('/api/voice', voiceRoutes);
app.use('/api/video', videoRoutes);
app.use('/api/avatar', avatarRoutes);

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Video Backend API',
    version: '1.0.0',
    environment: process.env.NODE_ENV,
    status: 'running'
  });
});

// Config endpoint
app.get('/config', (req, res) => {
  res.json({
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT,
    apiBaseUrl: process.env.API_BASE_URL,
    uploadPath: process.env.UPLOAD_PATH,
    maxFileSize: process.env.MAX_FILE_SIZE
  });
});

// Multer error handling middleware
app.use((err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        message: '文件大小超过限制（最大50MB）',
        error: 'FILE_TOO_LARGE'
      });
    } else if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        message: '文件数量超过限制（最多10个文件）',
        error: 'TOO_MANY_FILES'
      });
    } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        message: '不支持的文件字段',
        error: 'UNEXPECTED_FILE'
      });
    }
  }
  
  // 自定义文件类型错误
  if (err.message && err.message.includes('不支持的文件类型')) {
    return res.status(400).json({
      message: err.message,
      error: 'UNSUPPORTED_FILE_TYPE'
    });
  }
  
  next(err);
});

// General error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// --- Server Startup ---
try {
  initializeDatabase();
  app.listen(port, () => {
    console.log(`🚀 Video Backend API listening on port ${port}`);
  });
} catch (err) {
  console.error("❌ 启动服务器失败:", err);
  process.exit(1);
}