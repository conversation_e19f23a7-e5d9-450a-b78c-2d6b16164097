package com.example.common.response;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 *
 * <AUTHOR> Generator
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(0, ""),

    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    UNPROCESSABLE_ENTITY(422, "请求参数校验失败"),
    VALIDATION_ERROR(422, "请求参数校验失败"),

    // 服务端错误
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误
    BUSINESS_ERROR(600, "业务处理失败"),
    DATA_NOT_FOUND(601, "数据不存在"),
    DATA_EXISTS(602, "数据已存在"),
    DATA_INVALID(603, "数据无效"),
    OPERATION_FAILED(604, "操作失败"),

    // 系统错误
    SYSTEM_ERROR(700, "系统错误"),
    DATABASE_ERROR(701, "数据库操作失败"),
    CACHE_ERROR(702, "缓存操作失败"),
    THIRD_PARTY_ERROR(703, "第三方服务调用失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 消息
     */
    private final String message;
} 