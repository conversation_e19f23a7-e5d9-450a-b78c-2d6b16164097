import { useState, useCallback } from 'react';
import { useApp } from '../context/AppContext.jsx';
import { createFileObject, validateFileSize } from '../utils/fileUtils';
import { App } from 'antd';
import apiService from '../api/index.js';

export function useFileUpload({ files, setFiles }) {
  const { state } = useApp(); // Keep for projectId, can be passed as arg if preferred
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);
  const { message } = App.useApp();

  const uploadFile = useCallback(async (file, projectId) => {
    try {
      setUploading(true);
      const result = await apiService.uploadAsset(projectId, file);
      return result;
    } catch (error) {
      console.error('File upload failed:', error);
      message.error('文件上传失败: ' + error.message);
      throw error;
    } finally {
      setUploading(false);
    }
  }, [message]);

  const addFile = useCallback(async (file) => {
    if (!validateFileSize(file, 50)) { // 50MB limit
      message.error('文件大小不能超过50MB');
      return false;
    }

    const fileObject = createFileObject(file);
    
    if (state.projectId) {
      fileObject.status = 'uploading';
      setFiles(prev => [...prev, fileObject]);
      
      try {
        const uploadResult = await uploadFile(file, state.projectId);
        setFiles(prev => prev.map(f => f.id === fileObject.id ? {
          ...fileObject,
          status: 'uploaded',
          url: uploadResult.url,
          assetId: uploadResult.assetId,
          uploadedAt: uploadResult.uploadedAt
        } : f));
        message.success(`文件 ${file.name} 上传成功`);
      } catch (error) {
        setFiles(prev => prev.map(f => f.id === fileObject.id ? {
          ...fileObject, status: 'error', error: error.message
        } : f));
      }
    } else {
      fileObject.status = 'pending';
      setFiles(prev => [...prev, fileObject]);
    }
    
    return true;
  }, [setFiles, state.projectId, uploadFile, message]);

  const removeFile = useCallback((fileId) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
    // Here you might want to call an API to delete the file from the server as well
    // For example: apiService.deleteAsset(state.projectId, fileId);
  }, [setFiles]);

  const handleFileSelect = useCallback((selectedFiles) => {
    Array.from(selectedFiles).forEach(file => {
      addFile(file);
    });
  }, [addFile]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  // This function is no longer needed here as pending files logic should be handled by the component that creates a project
  // const uploadPendingFiles = ...

  return {
    uploadedFiles: files, // Return the state from props
    isDragOver,
    uploading,
    removeFile,
    handleFileSelect,
    handleDragOver,
    handleDragLeave,

    handleDrop,
    uploadFile,
  };
}
