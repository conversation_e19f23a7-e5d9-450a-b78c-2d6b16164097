<template>
  <div :class="containerClass">
    <a-spin :size="size" :spinning="true" />
    <div v-if="text" :class="textClass">
      {{ text }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'small' | 'default' | 'large'
  text?: string
  inline?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  text: '',
  inline: false
})

const containerClass = computed(() => {
  const classes = ['loading-spinner']
  if (props.inline) {
    classes.push('inline-flex items-center')
  } else {
    classes.push('flex flex-col items-center justify-center')
  }
  return classes.join(' ')
})

const textClass = computed(() => {
  const classes = ['loading-text']
  if (props.inline) {
    classes.push('ml-2 text-sm')
  } else {
    classes.push('mt-2 text-center')
  }
  return classes.join(' ')
})
</script>

<style scoped>
.loading-spinner {
  color: #1890ff;
}

.loading-text {
  color: #666;
  font-size: 14px;
}
</style> 