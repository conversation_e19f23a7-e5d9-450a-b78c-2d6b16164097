const express = require('express');
const router = express.Router();
const scriptController = require('../controllers/scriptController');

// Main script generation
router.post('/generate', scriptController.generateScripts);

// Get all scripts for a project
router.get('/project/:projectId', scriptController.getScriptsByProjectId);

// CRUD for individual scripts
router.post('/', scriptController.addScript); // Add new script
router.put('/:scriptId', scriptController.updateScript); // Update script
router.delete('/:scriptId', scriptController.deleteScript); // Delete script
router.post('/:scriptId/regenerate', scriptController.regenerateScript); // Regenerate script

// Get all shots for a project
router.get('/project/:projectId/shots', scriptController.getShotsByProjectId);

// Regenerate a single shot
router.post('/shots/:shotId/regenerate', scriptController.regenerateShot);

// Generate shots from scripts (async)
router.post('/project/:projectId/generate-shots', scriptController.triggerShotGeneration);

// Note: Regenerate routes are removed as they were mock-only and need DB logic to be reimplemented
// router.post('/regenerate', scriptController.regenerateScript);
// router.post('/shots/regenerate', scriptController.regenerateShot);

module.exports = router;
