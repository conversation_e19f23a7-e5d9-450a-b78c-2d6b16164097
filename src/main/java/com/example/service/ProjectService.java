package com.example.service;

import com.example.common.exception.BusinessException;
import com.example.common.jpa.JpaSpecifications;
import com.example.common.response.ResultCode;
import com.example.entity.Asset;
import com.example.entity.Project;
import com.example.entity.ScriptShot;
import com.example.param.asset.AssetRecordVO;
import com.example.param.project.ProjectCreateParam;
import com.example.param.project.ProjectRecordVO;
import com.example.param.project.ProjectUpdateParam;
import com.example.repository.AssetRepository;
import com.example.repository.ProjectRepository;
import com.example.repository.ScriptShotRepository;
import com.example.utils.FfmpegUtils;
import com.example.utils.VideoValidationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 项目服务类
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectService {

    @Value("${app.upload-path}")
    private String uploadPath;

    @Value("${app.static-path}")
    private String staticPath;

    @Value("${app.api-base-url}")
    private String apiBaseUrl;

    private final ProjectRepository projectRepository;
    private final AssetRepository assetRepository;
    private final ScriptShotRepository scriptShotRepository;

    /**
     * 创建项目
     */
    @Transactional
    public ProjectRecordVO createProject(ProjectCreateParam param) {
        log.info("创建项目: {}", param.getName());
        
        Project project = new Project();
        project.setId("proj_" + UUID.randomUUID().toString().replace("-", ""));
        project.setName(param.getName());
        project.setTheme(param.getTheme());
        project.setRequirements(param.getRequirements());
        project.setTextMaterial(param.getTextMaterial());
        project.setIsDraft(param.getIsDraft());
        project.setAvatarId(param.getAvatarId());
        project.setVoiceId(param.getVoiceId());
        project.setCreatedAt(LocalDateTime.now());
        project.setUpdatedAt(LocalDateTime.now());
        
        project = projectRepository.save(project);
        
        log.info("项目创建成功: {} - {}", project.getId(), project.getName());
        return ProjectRecordVO.from(project, List.of());
    }

    /**
     * 获取所有项目
     */
    public List<ProjectRecordVO> getAllProjects() {
        log.debug("获取所有项目");
        
        List<Project> projects = projectRepository.findAllByOrderByCreatedAtDesc();
        return projects.stream()
                .map(this::convertToProjectRecordVO)
                .toList();
    }

    /**
     * 根据ID获取项目
     */
    public ProjectRecordVO getProjectById(String projectId) {
        log.debug("获取项目详情: {}", projectId);
        
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));
        
        return convertToProjectRecordVO(project);
    }

    /**
     * 更新项目
     */
    @Transactional
    public ProjectRecordVO updateProject(String projectId, ProjectUpdateParam param) {
        log.info("更新项目: {}", projectId);
        
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));
        
        if (StringUtils.hasText(param.getName())) {
            project.setName(param.getName());
        }
        if (StringUtils.hasText(param.getTheme())) {
            project.setTheme(param.getTheme());
        }
        if (param.getRequirements() != null) {
            project.setRequirements(param.getRequirements());
        }
        if (param.getTextMaterial() != null) {
            project.setTextMaterial(param.getTextMaterial());
        }
        if (StringUtils.hasText(param.getAvatarId())) {
            project.setAvatarId(param.getAvatarId());
        }
        if (StringUtils.hasText(param.getVoiceId())) {
            project.setVoiceId(param.getVoiceId());
        }
        
        project = projectRepository.save(project);
        
        log.info("项目更新成功: {}", projectId);
        return convertToProjectRecordVO(project);
    }

    /**
     * 删除项目
     */
    @Transactional
    public void deleteProject(String projectId) {
        log.info("删除项目: {}", projectId);
        
        if (!projectRepository.existsById(projectId)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "项目不存在");
        }
        
        // 删除关联的资产
        assetRepository.deleteByProjectId(projectId);

        // 删除关联的脚本镜头
        scriptShotRepository.deleteByProjectId(projectId);

        // 删除项目
        projectRepository.deleteById(projectId);
        
        log.info("项目删除成功: {}", projectId);
    }

    /**
     * 根据条件查询项目
     */
    public List<ProjectRecordVO> findProjectsByConditions(String name, Boolean isDraft) {
        log.debug("根据条件查询项目: name={}, isDraft={}", name, isDraft);
        
        Specification<Project> spec = JpaSpecifications.<Project>and()
                .like(StringUtils.hasText(name), Project::getName, "%" + name + "%")
                .eq(isDraft != null, Project::getIsDraft, isDraft)
                .build();
        
        List<Project> projects = projectRepository.findAll(spec);
        return projects.stream()
                .map(this::convertToProjectRecordVO)
                .toList();
    }

    /**
     * 转换为ProjectRecordVO
     */
    private ProjectRecordVO convertToProjectRecordVO(Project project) {
        List<AssetRecordVO> assets = assetRepository.findByProjectIdOrderByUploadedAtDesc(project.getId())
                .stream()
                .map(AssetRecordVO::from)
                .toList();
        
        return ProjectRecordVO.from(project, assets);
    }

    @Transactional
    public AssetRecordVO uploadAsset(String projectId, MultipartFile file) {
        long size = file.getSize();
        // 1. 校验项目是否存在
        Project project = projectRepository.findById(projectId)
            .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));

        // 2. 保存文件到本地（或云存储）
        String uploadDir = String.join(File.separator, uploadPath, projectId);

        File dir = new File(uploadDir);
        if (!dir.exists()) dir.mkdirs();
        String fileName = UUID.randomUUID() + "_" + file.getOriginalFilename();
        File dest = new File(dir, fileName);
        try {
            file.transferTo(dest);
        } catch (IOException e) {
            log.error("上传文件失败: {}", e.getMessage());
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "上传文件失败");
        }
        String fileUrl = String.join(File.separator, apiBaseUrl, "uploads", projectId, fileName);

        // 3. 保存文件信息到数据库
        Asset asset = new Asset();
        asset.setId("asset_" + UUID.randomUUID().toString().replace("-", ""));
        asset.setProjectId(projectId);
        asset.setOriginalName(file.getOriginalFilename());
        asset.setFilename(file.getOriginalFilename());
        asset.setPath(dest.getAbsolutePath());
        asset.setSize(size);
        asset.setUrl(fileUrl);
        asset.setUploadedAt(LocalDateTime.now());
        assetRepository.save(asset);

        // 4. 返回VO
        return AssetRecordVO.from(asset);
    }

    public void deleteAsset(String assetId) {
        log.info("删除项目素材: assetId={}", assetId);
        assetRepository.deleteById(assetId);
    }

    public List<AssetRecordVO> getProjectAssets(String projectId) {
        log.info("获取项目素材: projectId={}", projectId);
        List<Asset> assets = assetRepository.findByProjectIdOrderByUploadedAtDesc(projectId);
        return assets.stream()
                .map(AssetRecordVO::from)
                .toList();
    }

    @Async
    @Transactional
    public void composeProject(String projectId) {
        log.info("开始合成项目视频: {}", projectId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));

        // 检查并更新合成状态
        if (project.getCompositionStatus() == Project.GenerationStatus.in_progress) {
            log.warn("项目 {} 正在合成中，请勿重复请求", projectId);
            return;
        }
        project.setCompositionStatus(Project.GenerationStatus.in_progress);
        projectRepository.save(project);

        try {
            // 获取项目的所有镜头，按顺序排列
            List<ScriptShot> shots = scriptShotRepository.findByProjectIdOrderByOrderIndexAsc(projectId);

            if (shots.isEmpty()) {
                throw new BusinessException(ResultCode.VALIDATION_ERROR, "项目没有可合成的镜头");
            }

            // 过滤出有最终视频路径的镜头，并记录详细信息
            List<String> videoPaths = new ArrayList<>();
            for (ScriptShot shot : shots) {
                String finalVideoPath = shot.getFinalVideoPath();
                if (StringUtils.hasText(finalVideoPath)) {
                    if (Files.exists(Paths.get(finalVideoPath))) {
                        try {
                            // 检查视频文件大小和时长
                            long fileSize = Files.size(Paths.get(finalVideoPath));
                            log.info("镜头 {} 视频文件: {}, 大小: {} bytes",
                                    shot.getId(), finalVideoPath, fileSize);
                            videoPaths.add(finalVideoPath);
                        } catch (IOException e) {
                            log.warn("无法读取视频文件信息: {}", finalVideoPath, e);
                        }
                    } else {
                        log.warn("镜头 {} 的视频文件不存在: {}", shot.getId(), finalVideoPath);
                    }
                } else {
                    log.warn("镜头 {} 没有最终视频路径", shot.getId());
                }
            }

            if (videoPaths.isEmpty()) {
                throw new BusinessException(ResultCode.VALIDATION_ERROR, "项目没有可合成的视频文件");
            }

            log.info("项目 {} 找到 {} 个有效视频文件准备合成", projectId, videoPaths.size());

            // 验证所有视频文件
            VideoValidationUtils.ValidationResult validationResult =
                    VideoValidationUtils.validateVideoList(videoPaths);

            log.info("视频验证结果: {}", validationResult);

            if (validationResult.hasErrors()) {
                log.error("视频验证失败: {}", validationResult.getErrors());
                throw new BusinessException(ResultCode.VALIDATION_ERROR,
                        "视频文件验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            if (validationResult.hasWarnings()) {
                log.warn("视频验证警告: {}", validationResult.getWarnings());
            }

            double totalDuration = validationResult.getTotalDuration();
            log.info("预计合成视频总时长: {}秒 ({}分钟)", totalDuration, totalDuration / 60);

            // 生成最终合成视频的文件名
            String finalVideoFileName = "final_" + projectId + "_" + System.currentTimeMillis() + ".mp4";
            String finalVideoPath = staticPath + File.separator + finalVideoFileName;

            // 确保输出目录存在
            Path outputDir = Paths.get(uploadPath);
            if (!Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            log.info("使用FFmpeg拼接视频: {} 个文件 -> {}", videoPaths.size(), finalVideoPath);

            // 使用 FfmpegUtils 拼接所有视频
            FfmpegUtils.concatenateVideos(videoPaths, finalVideoPath);

            // 验证最终视频是否生成成功
            if (Files.exists(Paths.get(finalVideoPath))) {
                try {
                    // 验证最终视频的时长
                    double finalDuration = VideoValidationUtils.getVideoDuration(finalVideoPath);
                    long finalSize = Files.size(Paths.get(finalVideoPath));

                    log.info("最终视频生成成功: 时长={}秒, 大小={}MB",
                            finalDuration, finalSize / (1024 * 1024));

                    // 检查时长是否合理（与预期总时长比较，允许5%的误差）
                    double expectedDuration = totalDuration;
                    double durationDifference = Math.abs(finalDuration - expectedDuration);
                    double toleranceRatio = 0.05; // 5%容差

                    if (durationDifference > expectedDuration * toleranceRatio) {
                        log.warn("最终视频时长异常: 实际={}秒, 预期={}秒, 差异={}秒",
                                finalDuration, expectedDuration, durationDifference);
                    }

                    String finalVideoUrl = apiBaseUrl + "/static/" + finalVideoFileName;
                    project.setFinalVideoUrl(finalVideoUrl);
                    project.setCompositionStatus(Project.GenerationStatus.completed);
                    log.info("项目 {} 视频合成成功: {}", projectId, finalVideoUrl);

                } catch (IOException e) {
                    log.warn("无法验证最终视频信息: {}", e.getMessage());
                    // 即使无法验证，也认为合成成功
                    String finalVideoUrl = apiBaseUrl + "/static/" + finalVideoFileName;
                    project.setFinalVideoUrl(finalVideoUrl);
                    project.setCompositionStatus(Project.GenerationStatus.completed);
                    log.info("项目 {} 视频合成成功: {}", projectId, finalVideoUrl);
                }
            } else {
                throw new BusinessException(ResultCode.OPERATION_FAILED, "视频合成失败");
            }

        } catch (IOException e) {
            log.error("项目 {} FFmpeg视频合成失败: {}", projectId, e.getMessage(), e);
            project.setCompositionStatus(Project.GenerationStatus.failed);
        } catch (BusinessException e) {
            log.error("项目 {} 视频合成业务异常: {}", projectId, e.getMessage());
            project.setCompositionStatus(Project.GenerationStatus.failed);
        } catch (Exception e) {
            log.error("项目 {} 视频合成失败", projectId, e);
            project.setCompositionStatus(Project.GenerationStatus.failed);
        } finally {
            projectRepository.save(project);
        }
    }

    /**
     * 预处理视频文件，确保所有视频具有相同的编解码器参数
     * 这对于FFmpeg的concat demuxer非常重要
     */
    private String preprocessVideoForConcatenation(String inputVideoPath, String projectId, int index) throws IOException {
        String preprocessedFileName = "preprocessed_" + projectId + "_" + index + "_" + System.currentTimeMillis() + ".mp4";
        String preprocessedVideoPath = uploadPath + File.separator + preprocessedFileName;

        log.info("预处理视频文件: {} -> {}", inputVideoPath, preprocessedVideoPath);

        // 使用FFmpeg标准化视频参数
        // 这里可以扩展FfmpegUtils来添加视频预处理功能
        // 目前先复制文件，在实际应用中应该添加重编码逻辑
        Files.copy(Paths.get(inputVideoPath), Paths.get(preprocessedVideoPath));

        return preprocessedVideoPath;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(List<String> tempFiles) {
        for (String tempFile : tempFiles) {
            try {
                Files.deleteIfExists(Paths.get(tempFile));
                log.debug("清理临时文件: {}", tempFile);
            } catch (IOException e) {
                log.warn("清理临时文件失败: {}", tempFile, e);
            }
        }
    }
}