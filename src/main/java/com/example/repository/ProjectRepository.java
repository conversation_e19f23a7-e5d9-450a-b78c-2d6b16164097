package com.example.repository;

import com.example.entity.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 项目Repository接口
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Repository
public interface ProjectRepository extends JpaRepository<Project, String>, JpaSpecificationExecutor<Project> {

    /**
     * 根据草稿状态查询项目
     */
    List<Project> findByIsDraftOrderByUpdatedAtDesc(Boolean isDraft);

    /**
     * 根据项目名称查询项目
     */
    List<Project> findByNameContainingIgnoreCaseOrderByUpdatedAtDesc(String name);

    List<Project> findAllByOrderByCreatedAtDesc();
} 