<template>
  <div class="bg-white rounded-lg p-6 shadow-sm">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">时间线</h3>
    
    <!-- Timeline Container -->
    <div class="relative">
      <!-- Time Markers -->
      <div class="flex justify-between text-xs text-gray-500 mb-2">
        <span>00:00</span>
        <span>00:30</span>
        <span>01:00</span>
        <span>01:30</span>
        <span>02:00</span>
        <span>02:30</span>
      </div>
      
      <!-- Timeline Track -->
      <div class="relative bg-gray-100 rounded-lg p-3">
        <!-- Video Tracks -->
        <div class="space-y-2">
          <!-- Video Track -->
          <div class="flex items-center gap-2 mb-2">
            <div class="w-12 text-xs text-gray-600 font-medium">视频</div>
            <div class="flex-1 h-8 bg-blue-200 rounded relative overflow-hidden">
              <div
                v-for="(clip, index) in videoClips"
                :key="index"
                class="absolute top-0 h-full bg-blue-500 rounded border-r-2 border-blue-600 flex items-center justify-center"
                :style="{ 
                  left: `${clip.start}%`, 
                  width: `${clip.duration}%`,
                  backgroundColor: clip.color
                }"
              >
                <span class="text-xs text-white font-medium truncate px-1">
                  {{ clip.name }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- Audio Track -->
          <div class="flex items-center gap-2 mb-2">
            <div class="w-12 text-xs text-gray-600 font-medium">音频</div>
            <div class="flex-1 h-8 bg-green-200 rounded relative overflow-hidden">
              <div
                v-for="(clip, index) in audioClips"
                :key="index"
                class="absolute top-0 h-full bg-green-500 rounded border-r-2 border-green-600 flex items-center justify-center"
                :style="{ 
                  left: `${clip.start}%`, 
                  width: `${clip.duration}%`
                }"
              >
                <span class="text-xs text-white font-medium truncate px-1">
                  {{ clip.name }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- Avatar Track -->
          <div class="flex items-center gap-2">
            <div class="w-12 text-xs text-gray-600 font-medium">数字人</div>
            <div class="flex-1 h-8 bg-purple-200 rounded relative overflow-hidden">
              <div
                v-for="(clip, index) in avatarClips"
                :key="index"
                class="absolute top-0 h-full bg-purple-500 rounded border-r-2 border-purple-600 flex items-center justify-center"
                :style="{ 
                  left: `${clip.start}%`, 
                  width: `${clip.duration}%`
                }"
              >
                <span class="text-xs text-white font-medium truncate px-1">
                  {{ clip.name }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Playhead -->
        <div
          class="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 transition-all duration-300"
          :style="{ left: `${playheadPosition}%` }"
        >
          <div class="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full"></div>
        </div>
      </div>
      
      <!-- Timeline Controls -->
      <div class="flex items-center justify-between mt-4">
        <div class="flex items-center gap-2">
          <a-button size="small" @click="play">
            <PlayCircleOutlined v-if="!isPlaying" />
            <PauseCircleOutlined v-else />
          </a-button>
          <a-button size="small" @click="stop">
            <StopOutlined />
          </a-button>
          <span class="text-sm text-gray-600 ml-2">
            {{ formatTime(currentTime) }} / {{ formatTime(totalTime) }}
          </span>
        </div>
        
        <div class="flex items-center gap-2">
          <span class="text-xs text-gray-600">缩放:</span>
          <a-slider
            v-model:value="zoomLevel"
            :min="50"
            :max="200"
            :step="10"
            class="w-20"
            size="small"
          />
          <span class="text-xs text-gray-600">{{ zoomLevel }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined } from '@ant-design/icons-vue'

interface TimelineClip {
  name: string
  start: number
  duration: number
  color?: string
}

const isPlaying = ref(false)
const currentTime = ref(0)
const totalTime = ref(150) // 2:30 in seconds
const playheadPosition = ref(0)
const zoomLevel = ref(100)

let playInterval: ReturnType<typeof setInterval> | null = null

// 模拟视频片段数据
const videoClips = reactive<TimelineClip[]>([
  { name: '镜头1', start: 0, duration: 20, color: '#3B82F6' },
  { name: '镜头2', start: 20, duration: 25, color: '#1D4ED8' },
  { name: '镜头3', start: 45, duration: 30, color: '#3B82F6' },
  { name: '镜头4', start: 75, duration: 25, color: '#1D4ED8' }
])

const audioClips = reactive<TimelineClip[]>([
  { name: '配音1', start: 0, duration: 50 },
  { name: '配音2', start: 50, duration: 50 }
])

const avatarClips = reactive<TimelineClip[]>([
  { name: 'Emma', start: 0, duration: 40 },
  { name: 'Alex', start: 40, duration: 60 }
])

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const play = () => {
  if (isPlaying.value) {
    pause()
  } else {
    isPlaying.value = true
    playInterval = setInterval(() => {
      currentTime.value += 1
      playheadPosition.value = (currentTime.value / totalTime.value) * 100
      
      if (currentTime.value >= totalTime.value) {
        stop()
      }
    }, 1000)
  }
}

const pause = () => {
  isPlaying.value = false
  if (playInterval) {
    clearInterval(playInterval)
    playInterval = null
  }
}

const stop = () => {
  pause()
  currentTime.value = 0
  playheadPosition.value = 0
}

onUnmounted(() => {
  if (playInterval) {
    clearInterval(playInterval)
  }
})
</script> 