import { useState, useEffect } from 'react';
import { Input, App } from 'antd';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import { useApi } from '../../hooks/useApi.js';
import Button from '../common/Button';
import ErrorDisplay from '../common/ErrorDisplay';
import LoadingSpinner from '../common/LoadingSpinner';
import MaterialSourceSelector from './MaterialSourceSelector';
import FileUpload from './FileUpload';

const { TextArea } = Input;

const UploadContent = () => {
  const { state: appState } = useApp();
  const { setCurrentTab } = useNavigation();
  const { getProject, updateProject, generateScripts } = useApi();
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [projectData, setProjectData] = useState({
      name: '未命名项目',
      theme: '',
      requirements: '',
      text_material: '',
  });
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [materialSource, setMaterialSource] = useState('upload');
  const { message } = App.useApp();

  useEffect(() => {
    const loadProjectData = async () => {
      if (appState.projectId) {
        setIsLoading(true);
        try {
          const project = await getProject(appState.projectId);
          if (project) {
            setProjectData({
              name: project.name || '未命名项目',
              theme: project.theme || '',
              requirements: project.requirements || '',
              text_material: project.text_material || '',
            });

            if (project.assets && project.assets.length > 0) {
              const files = project.assets.map(asset => ({
                id: asset.id,
                file: { name: asset.original_name, size: asset.file_size },
                status: 'uploaded',
                progress: 100,
                path: asset.file_path,
              }));
              setUploadedFiles(files);
            }
          }
        } catch (error) {
          console.error('加载项目数据失败:', error);
          message.error('加载项目数据失败，请刷新页面重试');
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };
    loadProjectData();
  }, [appState.projectId, getProject, message]);

  const handleInputChange = (field, value) => {
    setProjectData(prev => ({ ...prev, [field]: value }));
  };

  const handleTextMaterialChange = (value) => {
    setProjectData(prev => ({ ...prev, text_material: value }));
  };

  const handleStartGeneration = async () => {
    if (materialSource === 'text' && !projectData.text_material.trim()) {
      message.warning('请输入文本素材');
      return;
    }

    if (materialSource === 'upload' && uploadedFiles.length === 0) {
      message.warning('请上传素材文件');
      return;
    }

    setIsGenerating(true);
    try {
      // Always update the project with the latest data before generating scripts
      await updateProject(appState.projectId, {
          name: projectData.name,
          theme: projectData.theme,
          requirements: projectData.requirements,
          textMaterial: projectData.text_material, // API expects textMaterial
      });

      // Fire and forget - don't wait for the script generation to complete
      generateScripts({ projectId: appState.projectId }).catch(err => {
          // Even if it fails, we navigate. The user will see the status on the next page.
          console.error("Initiating script generation failed:", err);
      });

      // Immediately navigate to the script page
      setCurrentTab('script');
      
    } catch (error) {
      console.error('生成脚本失败:', error);
      message.error('生成脚本失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
      return <div className="flex justify-center items-center p-8"><LoadingSpinner text="正在加载项目数据..."/></div>
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          创建新项目
        </h2>
        <p className="text-gray-600">
          填写项目信息并上传相关素材，AI将为您生成精彩视频
        </p>
      </div>

      {/* Form Container */}
      <div className="max-w-4xl bg-white rounded-lg p-8 shadow-sm">
        {/* Project Name */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            项目名称
          </label>
          <Input
            value={projectData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="给项目起个名字"
            size="large"
          />
        </div>

        {/* Project Theme */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            视频主题
          </label>
          <Input
            value={projectData.theme}
            onChange={(e) => handleInputChange('theme', e.target.value)}
            placeholder="例如：产品介绍、企业宣传、教育培训等"
            size="large"
          />
        </div>

        {/* Project Requirements */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            详细需求
          </label>
          <TextArea
            value={projectData.requirements}
            onChange={(e) => handleInputChange('requirements', e.target.value)}
            placeholder="描述您对视频的具体要求，包括目标受众、核心信息、时长等"
            rows={4}
            size="large"
          />
        </div>

        {/* Material Source Selector */}
        <MaterialSourceSelector source={materialSource} setSource={setMaterialSource} />

        {/* File Upload or Text Input */}
        {materialSource === 'upload' ? (
          <FileUpload files={uploadedFiles} setFiles={setUploadedFiles} />
        ) : (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文本素材
            </label>
            <TextArea
              value={projectData.text_material}
              onChange={(e) => handleTextMaterialChange(e.target.value)}
              placeholder="请在此处粘贴或输入您的文本素材，例如文章、大纲、核心要点等..."
              rows={8}
              size="large"
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-4">
          <Button
            variant="secondary"
            onClick={() => updateProject(appState.projectId, {
                name: projectData.name,
                theme: projectData.theme,
                requirements: projectData.requirements,
                textMaterial: projectData.text_material
            }).then(() => message.success('草稿已保存'))}
            disabled={isGenerating}
          >
            保存草稿
          </Button>
          <Button
            variant="primary"
            onClick={handleStartGeneration}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" text="" className="mr-2" />
                正在生成脚本...
              </div>
            ) : (
              '开始生成脚本'
            )}
          </Button>
        </div>

        {/* 错误信息显示 */}
        <ErrorDisplay className="mt-4" />

        {/* API连接状态指示器 */}
        {!appState.apiConnected && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              ⚠️ 后端API连接异常，请确保后端服务正在运行 (http://localhost:3001)
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadContent;
