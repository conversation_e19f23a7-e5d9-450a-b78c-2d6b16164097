# 开发环境配置 - 仅环境特定配置
spring:
  # 开发环境数据源配置
  datasource:
    url: jdbc:sqlite:${DB_PATH:/Users/<USER>/video-project/database.sqlite}
    username: # SQLite不需要用户名
    password: # SQLite不需要密码
    hikari:
      minimum-idle: 1
      maximum-pool-size: 1
  
  # 开发环境JPA配置
  jpa:
    hibernate:
      ddl-auto: update

# 开发环境日志配置
logging:
  level:
    root: INFO
    com.example: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.orm.jdbc.bind: TRACE  # 打印绑定参数

# Springdoc OpenAPI UI Configuration for Dev
springdoc:
  api-docs:
    enabled: true # Enable API docs for dev
  swagger-ui:
    enabled: true # Enable Swagger UI for dev
