<template>
  <div class="bg-white rounded-lg border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200">
    <!-- Mobile: Vertical Layout, Desktop: Horizontal Layout -->
    <div class="flex flex-col md:flex-row">
      <!-- Left: Shot Image Area -->
      <div class="md:w-80 md:h-44 h-48 bg-gray-50 relative flex-shrink-0">
        <!-- Shot Image -->
        <img
          v-if="shot.imageUrl"
          :src="shot.imageUrl"
          :alt="`镜头 ${shot.shotNumber}`"
          class="w-full h-full object-cover"
          :style="{ opacity: isLoading ? 0.5 : 1 }"
        />
        <div v-else class="w-full h-full flex items-center justify-center">
          <div class="text-center">
            <span class="material-icons text-4xl text-gray-400 mb-2 block">
              image
            </span>
            <p class="text-gray-500 text-sm">暂无图片</p>
          </div>
        </div>

        <!-- Shot Number -->
        <div class="absolute top-3 left-3 w-6 h-6 bg-black bg-opacity-70 text-white rounded-full flex items-center justify-center text-xs font-medium">
          {{ index + 1 }}
        </div>

        <!-- Regenerate Overlay -->
        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
          <button
            @click="handleRegenerateClick"
            class="bg-white bg-opacity-90 text-gray-800 px-3 py-2 rounded-full text-sm font-medium flex items-center gap-2 hover:bg-white transition-colors duration-200"
            :disabled="isLoading"
          >
            <AutoAwesomeIcon class="text-base" />
            重新生成
          </button>
        </div>

        <!-- Loading Overlay -->
        <div v-if="isLoading" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div class="text-center text-white">
            <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <span class="text-xs">生成中</span>
          </div>
        </div>

        <!-- Shot Status Badge -->
        <div class="absolute top-3 right-3">
          <div v-if="shot.imageUrl && !isLoading" class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
            已生成
          </div>
          <div v-else-if="isLoading" class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
            生成中
          </div>
          <div v-else class="bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium">
            待生成
          </div>
        </div>
      </div>

      <!-- Right: Content -->
      <div class="flex-1 p-4 flex flex-col justify-between">
        <!-- Top Section -->
        <div>
          <!-- Shot Title -->
          <h3 class="text-sm font-medium text-gray-900 mb-2">
            镜头 {{ shot.shotNumber }}
          </h3>
          
          <!-- Script Text -->
          <p class="text-sm text-gray-700 leading-relaxed mb-4 line-clamp-3">
            {{ shot.text }}
          </p>
        </div>

        <!-- Bottom Actions -->
        <div class="flex gap-2">
          <a-button
            @click="handleRegenerateClick"
            :disabled="isLoading"
            class="flex-1"
            type="primary"
          >
            {{ isLoading ? '生成中...' : '重新生成' }}
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { StarOutlined as AutoAwesomeIcon } from '@ant-design/icons-vue'

interface Shot {
  id: string
  shotNumber: number
  text: string
  imageUrl?: string
}

interface Props {
  shot: Shot
  index: number
  onRegenerate: (shot: Shot) => void
  isProcessing?: boolean
}

const props = defineProps<Props>()

const isLoading = ref(props.isProcessing || false)

const handleRegenerateClick = () => {
  if (!isLoading.value) {
    props.onRegenerate(props.shot)
  }
}

</script>

<style scoped>
.material-icons {
  font-family: 'Material Icons';
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 