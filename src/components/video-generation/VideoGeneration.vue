<template>
  <div>
    <!-- Loading State -->
    <template v-if="isLoadingShots">
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 mb-2">
          生成视频
        </h2>
        <p class="text-gray-600">
          为每个镜头选择合适的视频风格，AI将生成高质量的视频片段
        </p>
      </div>

      <div class="text-center py-12">
        <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto mb-4"></div>
        <p class="text-gray-500 text-lg">
          正在加载项目镜头数据...
        </p>
      </div>
    </template>

    <!-- Empty State -->
    <template v-else-if="shots.length === 0">
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 mb-2">
          生成视频
        </h2>
        <p class="text-gray-600">
          为每个镜头选择合适的视频风格，AI将生成高质量的视频片段
        </p>
      </div>
      
      <div class="text-center py-12">
        <VideoCameraOutlined class="text-6xl text-gray-300 mb-4 block mx-auto" />
        <p class="text-gray-500 text-lg">
          请先在"镜头编辑"页面创建镜头，才能生成对应的视频。
        </p>
        <a-button
          v-if="!isLoadingShots && appStore.projectId"
          type="default"
          @click="() => loadShotsData(appStore.projectId!)"
          class="mt-4"
        >
          重新加载镜头数据
        </a-button>
      </div>
    </template>

    <!-- Main Content -->
    <template v-else>
      <!-- Header -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 mb-2">
          生成视频
        </h2>
        <p class="text-gray-600">
          为每个镜头选择合适的视频风格，AI将生成高质量的视频片段
        </p>
      </div>

      <!-- Video Cards Grid -->
      <div class="grid grid-cols-1 gap-6 mb-8">
        <VideoCard
          v-for="(shot, index) in shots"
          :key="shot.id"
          :shot="shot"
          :index="index"
          :video-settings="shotVideos[shot.id] || {}"
          :is-generating="generatingShots.has(shot.id)"
          :on-style-select="(style) => handleStyleSelect(shot.id, style)"
          :on-resolution-select="(resolution) => handleResolutionSelect(shot.id, resolution)"
          :on-generate="() => handleGenerateVideo(shot.id)"
          :on-customize="() => openModal({ shotId: shot.id, shot })"
        />
      </div>

      <!-- Batch Actions -->
      <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              批量操作
            </h3>
            <p class="text-gray-600">
              为所有镜头快速生成视频，或统一设置视频风格
            </p>
          </div>
          <div class="flex gap-3">
            <a-button
              type="primary"
              @click="handleGenerateAll"
              :disabled="isGenerating || hasGeneratingVideos"
            >
              <template v-if="isGenerating || hasGeneratingVideos">
                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block align-middle"></div><span class="align-middle">正在生成全部视频...</span>
              </template>
              <template v-else>
                生成全部视频
              </template>
            </a-button>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div v-if="allVideosGenerated" class="flex justify-end">
        <a-button
          type="primary"
          @click="handleContinue"
        >
          确认视频并添加数字人
        </a-button>
      </div>
    </template>

    <!-- Video Generation Modal -->
    <VideoGenerationModal
      :is-open="isModalOpen"
      :on-close="closeModal"
      :shot-data="modalData"
      :on-generate="handleGenerateVideo"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { VideoCameraOutlined } from '@ant-design/icons-vue'
import { useAppStore } from '../../stores/app'
import { useNavigation } from '../../composables/useNavigation'
import { useApi } from '../../composables/useApi'
import VideoCard from './VideoCard.vue'
import VideoGenerationModal from './VideoGenerationModal.vue'

interface Shot {
  id: string
  projectId: string
  text: string
  orderIndex: number
  shotNumber: number
  imageUrl: string
  audioUrl?: string
  videoUrl?: string
  createdAt: string
  updatedAt: string
}

interface VideoSettings {
  style?: string
  quality?: string
  duration?: number
  resolution?: string
  isGenerated?: boolean
  videoUrl?: string | null
  thumbnailUrl?: string | null
}

interface ModalData {
  shotId: string
  shot: Shot
}

const appStore = useAppStore()
const { setCurrentTab } = useNavigation()
const { getShots, generateVideo } = useApi()

// 状态管理
const shots = ref<Shot[]>([])
const shotVideos = ref<Record<string, VideoSettings>>({})
const isGenerating = ref(false)
const generatingShots = ref(new Set<string>())
const isLoadingShots = ref(false)

// 模态框状态
const isModalOpen = ref(false)
const modalData = ref<ModalData | null>(null)

// 加载镜头数据
const loadShotsData = async (projectId: string) => {
  if (!projectId) return
  
  isLoadingShots.value = true
  try {
    const data = await getShots(projectId)
    shots.value = data.map((shot: any) => ({
      id: shot.id,
      projectId: shot.projectId,
      text: shot.text,
      orderIndex: shot.orderIndex,
      shotNumber: shot.shotNumber,
      imageUrl: shot.imageUrl,
      audioUrl: shot.audioUrl,
      videoUrl: shot.videoUrl,
      createdAt: shot.createdAt,
      updatedAt: shot.updatedAt
    }))
    
    // 初始化每个镜头的视频设置
    const initialVideos: Record<string, VideoSettings> = {}
    shots.value.forEach(shot => {
      initialVideos[shot.id] = {
        style: 'realistic',
        quality: 'high',
        duration: 10,
        resolution: '1080p',
        isGenerated: !!shot.videoUrl,
        videoUrl: shot.videoUrl || null,
        thumbnailUrl: shot.imageUrl // 使用镜头图片作为缩略图
      }
    })
    shotVideos.value = { ...shotVideos.value, ...initialVideos }
  } catch (error) {
    console.error('❌ 加载镜头数据失败:', error)
    message.error('加载镜头数据失败')
  } finally {
    isLoadingShots.value = false
  }
}

// 选择视频风格
const handleStyleSelect = (shotId: string, style: string) => {
  shotVideos.value = {
    ...shotVideos.value,
    [shotId]: {
      ...shotVideos.value[shotId],
      style,
      isGenerated: false,
      videoUrl: null
    }
  }
}

// 选择分辨率
const handleResolutionSelect = (shotId: string, resolution: string) => {
  shotVideos.value = {
    ...shotVideos.value,
    [shotId]: {
      ...shotVideos.value[shotId],
      resolution,
      isGenerated: false,
      videoUrl: null
    }
  }
}

// 生成单个视频
const handleGenerateVideo = async (shotId: string) => {
  generatingShots.value = new Set([...generatingShots.value, shotId])
  
  try {
    const shot = shots.value.find(s => s.id === shotId)
    if (!shot) {
      throw new Error('未找到对应的镜头')
    }

    const videoSettings = shotVideos.value[shotId]
    
    // 调用API生成视频
    const updatedShot = await generateVideo({
      shotId: shotId,
      duration: videoSettings.duration,
      resolution: videoSettings.resolution
    })
    
    
    // 验证返回的数据
    if (!updatedShot || !updatedShot.videoUrl) {
      throw new Error('视频生成失败：未返回有效的视频URL')
    }
    
    // 更新镜头数据
    const shotIndex = shots.value.findIndex(s => s.id === shotId)
    if (shotIndex !== -1) {
      shots.value[shotIndex] = {
        ...shots.value[shotIndex],
        videoUrl: updatedShot.videoUrl,
        updatedAt: updatedShot.updatedAt || new Date().toISOString()
      }
    }
    
    // 更新视频设置
    shotVideos.value = {
      ...shotVideos.value,
      [shotId]: {
        ...shotVideos.value[shotId],
        isGenerated: true,
        videoUrl: updatedShot.videoUrl,
        thumbnailUrl: updatedShot.imageUrl || shot.imageUrl
      }
    }
    
    message.success(`镜头视频生成成功`)
  } catch (error) {
    console.error('❌ 视频生成失败:', error)
    const errorMessage = error instanceof Error ? error.message : '视频生成失败'
    message.error(errorMessage)
    throw error // 重新抛出错误以便批量处理时能够捕获
  } finally {
    const newSet = new Set(generatingShots.value)
    newSet.delete(shotId)
    generatingShots.value = newSet
  }
}

// 批量生成视频
const handleGenerateAll = async () => {
  isGenerating.value = true
  
  try {
    // 获取所有未生成的镜头
    const unGeneratedShots = shots.value.filter(shot => !shotVideos.value[shot.id]?.isGenerated)
    
    if (unGeneratedShots.length === 0) {
      message.info('所有镜头视频都已生成')
      return
    }
    
    // 将所有未生成的镜头标记为生成中
    const newGeneratingShots = new Set(generatingShots.value)
    unGeneratedShots.forEach(shot => newGeneratingShots.add(shot.id))
    generatingShots.value = newGeneratingShots
    
    // 并行生成所有未生成的镜头视频
    const promises = unGeneratedShots.map(async (shot) => {
      try {
        const videoSettings = shotVideos.value[shot.id]
        
        // 调用API生成视频
        const response = await generateVideo({
          shotId: shot.id,
          duration: videoSettings.duration,
          resolution: videoSettings.resolution
        })
        
        // 处理返回的数据
        const updatedShot = response
        
        // 更新镜头数据
        const shotIndex = shots.value.findIndex(s => s.id === shot.id)
        if (shotIndex !== -1) {
          shots.value[shotIndex] = {
            ...shots.value[shotIndex],
            videoUrl: updatedShot.videoUrl,
            updatedAt: updatedShot.updatedAt || new Date().toISOString()
          }
        }
        
        // 更新视频设置
        shotVideos.value = {
          ...shotVideos.value,
          [shot.id]: {
            ...shotVideos.value[shot.id],
            isGenerated: true,
            videoUrl: updatedShot.videoUrl,
            thumbnailUrl: updatedShot.imageUrl || shot.imageUrl
          }
        }
        
        return { success: true, shotId: shot.id, shotNumber: shot.shotNumber }
      } catch (error) {
        console.error(`镜头 ${shot.shotNumber} 生成失败:`, error)
        return { success: false, shotId: shot.id, shotNumber: shot.shotNumber, error }
      }
    })

    const results = await Promise.all(promises)
    
    // 清理生成中状态
    generatingShots.value = new Set()
    
    // 统计结果
    const successCount = results.filter(r => r.success).length
    const failedShots = results.filter(r => !r.success)
    
    if (successCount === unGeneratedShots.length) {
      message.success(`成功生成 ${successCount} 个视频`)
    } else {
      message.warning(`成功生成 ${successCount}/${unGeneratedShots.length} 个视频`)
      if (failedShots.length > 0) {
        console.error('生成失败的镜头:', failedShots)
      }
    }
  } catch (error) {
    console.error('❌ 批量视频生成失败:', error)
    message.error('批量视频生成失败')
    generatingShots.value = new Set()
  } finally {
    isGenerating.value = false
  }
}

// 继续到下一步
const handleContinue = () => {
  setCurrentTab('avatar')
}

// 打开模态框
const openModal = (data: ModalData) => {
  modalData.value = data
  isModalOpen.value = true
}

// 关闭模态框
const closeModal = () => {
  modalData.value = null
  isModalOpen.value = false
}

// 计算是否所有视频都已生成
const allVideosGenerated = computed(() => {
  return shots.value.every(shot =>
    shotVideos.value[shot.id]?.isGenerated
  )
})

// 计算是否有正在生成的视频
const hasGeneratingVideos = computed(() => {
  return generatingShots.value.size > 0
})

// 组件挂载时的初始化
onMounted(() => {
  // 初始化逻辑可以在这里添加
})

// 监听项目ID变化
watch(() => appStore.projectId, (newProjectId) => {
  if (newProjectId) {
    loadShotsData(newProjectId)
  }
}, { immediate: true })
</script>

<style>
.border-t-primary {
  border-top-color: #1890ff;
}
</style> 