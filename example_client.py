#!/usr/bin/env python3
"""
Wav2Lip API 客户端示例
演示如何使用优化后的Wav2Lip服务器API
"""

import requests
import time
import os
from pathlib import Path

class Wav2LipClient:
    """Wav2Lip API 客户端"""
    
    def __init__(self, base_url="http://localhost:1206"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def health_check(self):
        """检查服务器健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def upload_files(self, face_path, audio_path):
        """上传人脸图片和音频文件"""
        try:
            with open(face_path, 'rb') as face_file, open(audio_path, 'rb') as audio_file:
                files = {
                    'face': (os.path.basename(face_path), face_file, 'image/jpeg'),
                    'audio': (os.path.basename(audio_path), audio_file, 'audio/wav')
                }
                
                response = self.session.post(f"{self.base_url}/upload", files=files)
                return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def start_synthesis(self, task_id, fps=25):
        """开始视频合成"""
        try:
            data = {"fps": fps}
            response = self.session.post(f"{self.base_url}/synthesize/{task_id}", json=data)
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def get_status(self, task_id):
        """获取任务状态"""
        try:
            response = self.session.get(f"{self.base_url}/status/{task_id}")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def download_result(self, task_id, output_path):
        """下载结果视频"""
        try:
            response = self.session.get(f"{self.base_url}/download/{task_id}")
            if response.status_code == 200:
                with open(output_path, 'wb') as f:
                    f.write(response.content)
                return {"success": True, "path": output_path}
            else:
                return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def cleanup_task(self, task_id):
        """清理任务文件"""
        try:
            response = self.session.delete(f"{self.base_url}/cleanup/{task_id}")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def list_tasks(self):
        """列出所有任务"""
        try:
            response = self.session.get(f"{self.base_url}/tasks")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def process_video(self, face_path, audio_path, output_path, fps=25, cleanup=True):
        """完整的视频处理流程"""
        print("🚀 开始Wav2Lip视频合成流程...")
        
        # 1. 健康检查
        print("📋 检查服务器状态...")
        health = self.health_check()
        if 'error' in health:
            print(f"❌ 服务器连接失败: {health['error']}")
            return False
        
        if not health.get('model_loaded'):
            print("❌ 模型未加载")
            return False
        
        print(f"✅ 服务器正常，使用设备: {health.get('device', 'unknown')}")
        
        # 2. 上传文件
        print("📤 上传文件...")
        upload_result = self.upload_files(face_path, audio_path)
        if 'error' in upload_result:
            print(f"❌ 文件上传失败: {upload_result['error']}")
            return False
        
        task_id = upload_result['task_id']
        print(f"✅ 文件上传成功，任务ID: {task_id}")
        
        # 3. 开始合成
        print("🎬 开始视频合成...")
        synthesis_result = self.start_synthesis(task_id, fps)
        if 'error' in synthesis_result:
            print(f"❌ 合成启动失败: {synthesis_result['error']}")
            if cleanup:
                self.cleanup_task(task_id)
            return False
        
        print("✅ 合成已启动，等待处理...")
        
        # 4. 监控进度
        while True:
            status = self.get_status(task_id)
            if 'error' in status:
                print(f"❌ 状态查询失败: {status['error']}")
                break
            
            current_status = status['status']
            progress = status.get('progress', 0)
            
            if current_status == 'completed':
                print(f"🎉 合成完成! 进度: {progress}%")
                break
            elif current_status == 'failed':
                error_msg = status.get('error', '未知错误')
                print(f"❌ 合成失败: {error_msg}")
                if cleanup:
                    self.cleanup_task(task_id)
                return False
            elif current_status in ['processing', 'uploaded']:
                print(f"🔄 处理中... 状态: {current_status}, 进度: {progress}%")
                time.sleep(2)  # 等待2秒后再次检查
            else:
                print(f"⚠️ 未知状态: {current_status}")
                time.sleep(2)
        
        # 5. 下载结果
        print("📥 下载结果视频...")
        download_result = self.download_result(task_id, output_path)
        if 'error' in download_result:
            print(f"❌ 下载失败: {download_result['error']}")
            if cleanup:
                self.cleanup_task(task_id)
            return False
        
        print(f"✅ 视频下载成功: {output_path}")
        
        # 6. 清理资源（可选）
        if cleanup:
            print("🧹 清理服务器资源...")
            cleanup_result = self.cleanup_task(task_id)
            if 'error' not in cleanup_result:
                print("✅ 资源清理完成")
        
        print("🎊 Wav2Lip视频合成流程完成!")
        return True

def main():
    """主函数 - 使用示例"""
    # 配置文件路径（请根据实际情况修改）
    face_image = "/Users/<USER>/Downloads/0f416e31-54ae-407f-9566-6ed68a0215ed.jpeg"  # 人脸图片路径
    audio_file = "/Users/<USER>/video-project/static/zf_xiaoyi.wav"  # 音频文件路径
    output_video = "result.mp4"      # 输出视频路径
    
    # 检查输入文件是否存在
    if not os.path.exists(face_image):
        print(f"❌ 人脸图片不存在: {face_image}")
        print("请将人脸图片放在当前目录并命名为 'example_face.jpg'")
        return
    
    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        print("请将音频文件放在当前目录并命名为 'example_audio.wav'")
        return
    
    # 创建客户端
    client = Wav2LipClient()
    
    # 执行完整流程
    success = client.process_video(
        face_path=face_image,
        audio_path=audio_file,
        output_path=output_video,
        fps=25,  # 视频帧率
        cleanup=True  # 处理完成后清理服务器文件
    )
    
    if success:
        print(f"\n🎉 成功! 结果视频保存在: {output_video}")
    else:
        print("\n❌ 处理失败，请检查错误信息")

def demo_api_usage():
    """演示各个API接口的使用方法"""
    client = Wav2LipClient()
    
    print("=== Wav2Lip API 使用演示 ===\n")
    
    # 1. 健康检查
    print("1. 健康检查:")
    health = client.health_check()
    print(f"   响应: {health}\n")
    
    # 2. 列出当前任务
    print("2. 当前任务列表:")
    tasks = client.list_tasks()
    print(f"   任务数量: {len(tasks) if isinstance(tasks, dict) else 0}")
    for task_id, task_info in (tasks.items() if isinstance(tasks, dict) else []):
        print(f"   - {task_id}: {task_info.get('status', 'unknown')}")
    print()
    
    # 如果有示例文件，演示上传和处理
    if os.path.exists("/Users/<USER>/Downloads/0f416e31-54ae-407f-9566-6ed68a0215ed.jpeg") and os.path.exists("/Users/<USER>/video-project/static/zf_xiaoyi.wav"):
        print("3. 文件上传演示:")
        upload_result = client.upload_files("/Users/<USER>/Downloads/0f416e31-54ae-407f-9566-6ed68a0215ed.jpeg", "/Users/<USER>/video-project/static/zf_xiaoyi.wav")
        print(f"   上传结果: {upload_result}")
        
        if 'task_id' in upload_result:
            task_id = upload_result['task_id']
            
            print(f"\n4. 开始合成 (任务ID: {task_id}):")
            synthesis_result = client.start_synthesis(task_id)
            print(f"   合成结果: {synthesis_result}")
            
            print(f"\n5. 查询状态:")
            status = client.get_status(task_id)
            print(f"   当前状态: {status}")
    else:
        print("3. 跳过上传演示 (缺少示例文件)")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_api_usage()
    else:
        main() 