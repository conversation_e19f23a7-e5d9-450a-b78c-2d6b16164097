package com.example.common.option;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TreeOption extends Option {

    List<TreeOption> children = new ArrayList<>();

    public TreeOption(String label, String value, List<TreeOption> children) {
        super(label, value);
        this.children = children;
    }
}
