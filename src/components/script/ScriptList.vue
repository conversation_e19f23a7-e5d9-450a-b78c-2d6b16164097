<template>
  <div v-if="!scripts || scripts.length === 0" class="text-center py-8 bg-gray-50 rounded-lg border border-gray-100 text-gray-400">
    <FileTextOutlined class="text-2xl mb-2 block" />
    <p class="text-sm">暂无脚本内容</p>
    <p class="text-xs text-gray-300 mt-1">点击下方的添加新条目来创建脚本</p>
  </div>

  <div 
    v-else
    @dragover.prevent
    @dragleave="handleGlobalDragLeave"
  >
    <div 
      v-for="(script, index) in scripts"
      :key="script.id"
      class="relative"
    >
      <!-- 插入位置指示器 - 顶部 -->
      <div 
        v-if="dropIndex === index && draggedIndex !== index"
        class="absolute left-0 right-0 h-0.5 -top-px flex items-center justify-center animate-fadeIn z-[100] pointer-events-none"
      >
        <div class="drop-line"></div>
        <div class="bg-blue-500 text-white px-2 py-0.5 rounded-xl text-xs font-medium z-10 shadow-md whitespace-nowrap">
          在此位置插入脚本
        </div>
      </div>
      
      <div 
        :class="[
          'cursor-move transition-all duration-200 ease-in-out relative',
          'hover:transform hover:-translate-y-px hover:shadow-md',
          {
            'opacity-50 transform rotate-1 scale-[0.98] z-[1000]': draggedIndex === index,
            'bg-blue-50 rounded-lg': dropIndex === index && draggedIndex !== index,
            'cursor-default': script.isNew
          }
        ]"
        :draggable="!script.isNew"
        @dragstart="handleDragStart(script, index, $event)"
        @dragover.prevent="handleDragOver(index, $event)"
        @dragleave="handleDragLeave($event)"
        @drop="handleDrop(script, index, $event)"
      >
        <ScriptItem
          :script="script"
          :index="index"
          @update-scripts="updateScripts"
          @get-scripts="getScripts"
          @refresh-scripts="handleRefreshScripts"
          @insert-before="(idx: number) => handleInsertBefore(idx)"
        />
      </div>
      
      <!-- 插入位置指示器 - 底部（最后一个元素时） -->
      <div 
        v-if="index === scripts.length - 1 && dropIndex === scripts.length && draggedIndex !== -1"
        class="absolute left-0 right-0 h-0.5 -bottom-px flex items-center justify-center animate-fadeIn z-[100] pointer-events-none"
      >
        <div class="drop-line"></div>
        <div class="bg-blue-500 text-white px-2 py-0.5 rounded-xl text-xs font-medium z-10 shadow-md whitespace-nowrap">
          在此位置插入脚本
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { FileTextOutlined } from '@ant-design/icons-vue'
import ScriptItem from './ScriptItem.vue'

interface Script {
  id: string
  text: string
  isNew?: boolean
  order_index: number
}

interface Props {
  scripts: Script[]
}

interface Emits {
  (e: 'update:scripts', scripts: Script[]): void
  (e: 'refresh-scripts'): void
  (e: 'insert-script', index: number): void
  (e: 'reorder-scripts', order: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

let draggedScript: Script | null = null
let draggedIndex: number = -1
const dropIndex = ref<number>(-1)

const updateScripts = (newScripts: Script[]) => {
  emit('update:scripts', newScripts)
}

const getScripts = (): Script[] => {
  return props.scripts
}

const handleRefreshScripts = () => {
  emit('refresh-scripts')
}

const handleInsertBefore = (index: number) => {
  emit('insert-script', index)
}

const handleDragStart = (script: Script, index: number, event: DragEvent) => {
  if (script.isNew) return
  
  // 清理之前的状态
  if (dragOverTimer) {
    clearTimeout(dragOverTimer)
    dragOverTimer = null
  }
  
  draggedScript = script
  draggedIndex = index
  dropIndex.value = -1
  
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
  }
}

let dragOverTimer: number | null = null

const handleDragOver = (targetIndex: number, event: DragEvent) => {
  event.preventDefault()
  if (draggedIndex === -1) return
  
  // 清除之前的定时器
  if (dragOverTimer) {
    clearTimeout(dragOverTimer)
  }
  
  // 计算插入位置
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const y = event.clientY - rect.top
  const height = rect.height
  
  // 添加一些缓冲区，减少频繁切换
  const buffer = height * 0.1 // 10%的缓冲区
  let newDropIndex: number
  
  if (y < height / 2 - buffer) {
    newDropIndex = targetIndex
  } else if (y > height / 2 + buffer) {
    newDropIndex = targetIndex + 1
  } else {
    // 在缓冲区内，保持当前状态
    return
  }
  
  // 只有当dropIndex真的需要改变时才更新
  if (dropIndex.value !== newDropIndex) {
    dropIndex.value = newDropIndex
  }
}

const handleDragLeave = (event: DragEvent) => {
  // 延迟处理dragleave，因为可能只是移动到子元素
  if (dragOverTimer) {
    clearTimeout(dragOverTimer)
  }
  
  dragOverTimer = window.setTimeout(() => {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    // 检查鼠标是否真的离开了元素区域
    if (
      event.clientX < rect.left - 10 ||
      event.clientX > rect.right + 10 ||
      event.clientY < rect.top - 10 ||
      event.clientY > rect.bottom + 10
    ) {
      // 只有在没有其他拖拽操作时才清除
      if (draggedIndex !== -1) {
        dropIndex.value = -1
      }
    }
  }, 100)
}

const handleGlobalDragLeave = (event: DragEvent) => {
  // 如果鼠标离开了整个脚本列表区域，清除dropIndex
  if (dragOverTimer) {
    clearTimeout(dragOverTimer)
  }
  
  dragOverTimer = window.setTimeout(() => {
    if (draggedIndex !== -1) {
      dropIndex.value = -1
    }
  }, 150)
}

const handleDrop = (targetScript: Script, targetIndex: number, event: DragEvent) => {
  event.preventDefault()
  
  if (!draggedScript || draggedIndex === -1) {
    return
  }
  
  // 使用dropIndex作为实际的插入位置
  const actualDropIndex = dropIndex.value !== -1 ? dropIndex.value : targetIndex
  
  if (draggedIndex === actualDropIndex) {
    // 重置状态
    draggedScript = null
    draggedIndex = -1
    dropIndex.value = -1
    return
  }
  
  // 创建新的脚本顺序
  const newScripts = [...props.scripts]
  const [movedScript] = newScripts.splice(draggedIndex, 1)
  
  // 调整插入位置（如果原位置在插入位置之前）
  const adjustedIndex = draggedIndex < actualDropIndex ? actualDropIndex - 1 : actualDropIndex
  newScripts.splice(adjustedIndex, 0, movedScript)
  
  // 更新orderIndex
  newScripts.forEach((script, index) => {
    script.order_index = index
  })
  
  // 提取ID顺序并触发重新排序
  const newOrder = newScripts.map(s => s.id)
  emit('reorder-scripts', newOrder)
  
  // 清理定时器
  if (dragOverTimer) {
    clearTimeout(dragOverTimer)
    dragOverTimer = null
  }
  
  // 重置拖拽状态
  draggedScript = null
  draggedIndex = -1
  dropIndex.value = -1
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (dragOverTimer) {
    clearTimeout(dragOverTimer)
    dragOverTimer = null
  }
})
</script>

<style scoped>
/* 复杂的渐变线条效果，无法用Tailwind替换 */
.drop-line {
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    #3b82f6 10%, 
    #3b82f6 90%, 
    transparent 100%
  );
  border-radius: 1px;
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.3);
}

.drop-line::before {
  content: '';
  position: absolute;
  left: 8px;
  top: -3px;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.5);
}

.drop-line::after {
  content: '';
  position: absolute;
  right: 8px;
  top: -3px;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.5);
}

/* 淡入动画，Tailwind默认不包含此动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-in-out;
}
</style> 