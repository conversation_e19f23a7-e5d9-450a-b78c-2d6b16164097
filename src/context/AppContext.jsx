import { createContext, useContext, useReducer } from 'react';

// Initial state
const initialState = {
  currentTab: 'upload',
  projectName: '未命名项目',
  projectTheme: '',
  projectRequirements: '',
  materialSource: 'upload',
  uploadedFiles: [],
  textMaterial: '',
  scripts: [
    {
      id: 1,
      text: '清晨的阳光透过百叶窗，洒在整洁的办公桌上。一台笔记本电脑屏幕亮着，显示着公司的Logo。'
    },
    {
      id: 2,
      text: '特写：手指轻快地在键盘上敲击，屏幕上代码飞速滚动。'
    },
    {
      id: 3,
      text: '镜头切换至会议室，团队成员围坐在一起，激烈地讨论着。白板上写满了各种想法和流程图。'
    }
  ],
  shots: [],
  selectedVoice: 'professional-female',
  voiceGenerated: false,
  videoGenerated: false,
  generatedVideos: {},
  selectedAvatar: 'emma',
  avatarConfigured: false,
  videoComposed: false,
  composedVideoUrl: '',
  exportSettings: {
    quality: '1080p',
    format: 'MP4'
  },
  // API相关状态
  projectId: null,
  isLoading: false,
  error: null,
  availableVoices: [],
  availableAvatars: [],
  tasks: {},
  apiConnected: false
};

// Action types
const ActionTypes = {
  SET_CURRENT_TAB: 'SET_CURRENT_TAB',
  UPDATE_PROJECT_INFO: 'UPDATE_PROJECT_INFO',
  SET_MATERIAL_SOURCE: 'SET_MATERIAL_SOURCE',
  ADD_UPLOADED_FILE: 'ADD_UPLOADED_FILE',
  SET_UPLOADED_FILES: 'SET_UPLOADED_FILES',
  UPDATE_UPLOADED_FILE: 'UPDATE_UPLOADED_FILE',
  REMOVE_UPLOADED_FILE: 'REMOVE_UPLOADED_FILE',
  SET_TEXT_MATERIAL: 'SET_TEXT_MATERIAL',
  SET_SCRIPTS: 'SET_SCRIPTS',
  ADD_SCRIPT: 'ADD_SCRIPT',
  UPDATE_SCRIPT: 'UPDATE_SCRIPT',
  DELETE_SCRIPT: 'DELETE_SCRIPT',
  REGENERATE_SCRIPT: 'REGENERATE_SCRIPT',
  GENERATE_SHOTS: 'GENERATE_SHOTS',
  REGENERATE_SHOT: 'REGENERATE_SHOT',
  SET_SELECTED_VOICE: 'SET_SELECTED_VOICE',
  GENERATE_VOICE: 'GENERATE_VOICE',
  GENERATE_VIDEO: 'GENERATE_VIDEO',
  SET_GENERATED_VIDEOS: 'SET_GENERATED_VIDEOS',
  SET_SELECTED_AVATAR: 'SET_SELECTED_AVATAR',
  CONFIGURE_AVATAR: 'CONFIGURE_AVATAR',
  COMPOSE_VIDEO: 'COMPOSE_VIDEO',
  UPDATE_EXPORT_SETTINGS: 'UPDATE_EXPORT_SETTINGS',
  // API相关动作
  SET_PROJECT_ID: 'SET_PROJECT_ID',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_AVAILABLE_VOICES: 'SET_AVAILABLE_VOICES',
  SET_AVAILABLE_AVATARS: 'SET_AVAILABLE_AVATARS',
  UPDATE_TASK: 'UPDATE_TASK',
  SET_API_CONNECTED: 'SET_API_CONNECTED',
  CLEAR_PROJECT_DATA: 'CLEAR_PROJECT_DATA'
};

// Reducer function
function appReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_CURRENT_TAB:
      return { ...state, currentTab: action.payload };
    
    case ActionTypes.UPDATE_PROJECT_INFO:
      return { ...state, ...action.payload };
    
    case ActionTypes.SET_MATERIAL_SOURCE:
      return { ...state, materialSource: action.payload };
    
    case ActionTypes.ADD_UPLOADED_FILE:
      return { 
        ...state, 
        uploadedFiles: [...state.uploadedFiles, action.payload] 
      };
    
    case ActionTypes.SET_UPLOADED_FILES:
      return { ...state, uploadedFiles: action.payload };
    
    case ActionTypes.UPDATE_UPLOADED_FILE:
      return {
        ...state,
        uploadedFiles: state.uploadedFiles.map(file =>
          file.id === action.payload.id ? action.payload : file
        )
      };
    
    case ActionTypes.REMOVE_UPLOADED_FILE:
      return { 
        ...state, 
        uploadedFiles: state.uploadedFiles.filter(file => file.id !== action.payload) 
      };
    
    case ActionTypes.SET_TEXT_MATERIAL:
      return { ...state, textMaterial: action.payload };

    case ActionTypes.SET_SCRIPTS:
      return { ...state, scripts: action.payload };

    case ActionTypes.ADD_SCRIPT: {
      const newScript = {
        id: Date.now(),
        text: action.payload || ''
      };
      return { 
        ...state, 
        scripts: [...state.scripts, newScript] 
      };
    }
    case ActionTypes.UPDATE_SCRIPT:
      return {
        ...state,
        scripts: state.scripts.map(script =>
          script.id === action.payload.id
            ? { ...script, text: action.payload.text }
            : script
        )
      };
    
    case ActionTypes.DELETE_SCRIPT:
      return {
        ...state,
        scripts: state.scripts.filter(script => script.id !== action.payload)
      };
    
    case ActionTypes.REGENERATE_SCRIPT:
      return {
        ...state,
        scripts: state.scripts.map(script =>
          script.id === action.payload.id
            ? { ...script, text: action.payload.newText }
            : script
        )
      };
    
    case ActionTypes.GENERATE_SHOTS: {
      // 如果payload是数组，直接使用；否则基于脚本生成
      if (Array.isArray(action.payload)) {
        return { ...state, shots: action.payload };
      } else {
        const shots = state.scripts.map((script, index) => ({
          id: script.id,
          number: index + 1,
          scriptText: script.text,
          imageUrl: `https://picsum.photos/seed/${Math.floor(Math.random() * 1000)}/400/225`
        }));
        return { ...state, shots };
      }
    }
    case ActionTypes.REGENERATE_SHOT:
      return {
        ...state,
        shots: state.shots.map(shot =>
          shot.id === action.payload.id
            ? { ...shot, imageUrl: action.payload.newImageUrl }
            : shot
        )
      };
    
    case ActionTypes.SET_SELECTED_VOICE:
      return { ...state, selectedVoice: action.payload };

    case ActionTypes.GENERATE_VOICE:
      return { ...state, voiceGenerated: true };

    case ActionTypes.GENERATE_VIDEO:
      return { ...state, videoGenerated: true };

    case ActionTypes.SET_GENERATED_VIDEOS:
      return {
        ...state,
        generatedVideos: { ...state.generatedVideos, ...action.payload }
      };

    case ActionTypes.SET_SELECTED_AVATAR:
      return { ...state, selectedAvatar: action.payload };

    case ActionTypes.CONFIGURE_AVATAR:
      return { ...state, avatarConfigured: true };

    case ActionTypes.COMPOSE_VIDEO:
      return {
        ...state,
        videoComposed: true,
        composedVideoUrl: action.payload
      };

    case ActionTypes.UPDATE_EXPORT_SETTINGS:
      return {
        ...state,
        exportSettings: { ...state.exportSettings, ...action.payload }
      };

    // API相关动作处理
    case ActionTypes.SET_PROJECT_ID:
      return { ...state, projectId: action.payload };

    case ActionTypes.SET_LOADING:
      return { ...state, isLoading: action.payload };

    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload };

    case ActionTypes.SET_AVAILABLE_VOICES:
      return { ...state, availableVoices: action.payload };

    case ActionTypes.SET_AVAILABLE_AVATARS:
      return { ...state, availableAvatars: action.payload };

    case ActionTypes.UPDATE_TASK:
      return {
        ...state,
        tasks: { ...state.tasks, [action.payload.id]: action.payload }
      };

    case ActionTypes.SET_API_CONNECTED:
      return { ...state, apiConnected: action.payload };

    case ActionTypes.CLEAR_PROJECT_DATA:
      return {
        ...state,
        projectId: null,
        projectName: '未命名项目',
        projectTheme: '',
        projectRequirements: '',
        uploadedFiles: [],
        textMaterial: '',
        scripts: [],
        shots: [],
        tasks: {},
        generatedVideos: {},
        // Keep some state like apiConnected, available voices/avatars
      };

    default:
      return state;
  }
}

// Create context
const AppContext = createContext();

// Provider component
export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch, ActionTypes }}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hook to use the context
export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
