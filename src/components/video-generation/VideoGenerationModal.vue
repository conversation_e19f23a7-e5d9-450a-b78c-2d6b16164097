<template>
  <a-modal
    :open="isOpen"
    title="自定义视频设置"
    :width="600"
    class="video-generation-modal"
    @ok="handleGenerate"
    @cancel="handleCancel"
    ok-text="生成视频"
    cancel-text="取消"
  >
    <div class="space-y-4 max-h-[60vh] overflow-y-auto">
      <!-- Shot Preview -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          当前镜头
        </label>
        <div class="flex gap-3 p-3 bg-gray-50 rounded-lg">
          <img
            :src="shotData?.shot?.imageUrl"
            alt="镜头预览"
            class="w-[192px] h-[108px] object-cover rounded flex-shrink-0"
          />
          <div class="flex-1 min-w-0">
            <p class="text-sm text-gray-700 leading-relaxed line-clamp-2">
              {{ shotData?.shot?.text }}
            </p>
          </div>
        </div>
      </div>

      <!-- Video Settings - Compact Grid Layout -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div v-for="[key, values] in Object.entries(options)" :key="key">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ getSettingLabel(key) }}
          </label>
          <div class="space-y-1">
            <label
              v-for="option in values"
              :key="option.value"
              :class="[
                'flex items-center p-2 border rounded cursor-pointer transition-all duration-200',
                (settings as any)[key] === option.value
                  ? 'border-primary bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              ]"
            >
              <input
                type="radio"
                :name="key"
                :value="option.value"
                :checked="(settings as any)[key] === option.value"
                @change="() => handleSettingChange(key, option.value)"
                class="w-4 h-4 text-primary focus:ring-primary border-gray-300 mr-2"
              />
              <div class="flex-1 min-w-0 ml-2">
                <div class="text-sm font-medium text-gray-900">
                  {{ option.label }}
                </div>
                <div class="text-xs text-gray-500 truncate">
                  {{ option.description }}
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Custom Prompt -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          自定义要求 (可选)
        </label>
        <a-textarea
          v-model:value="settings.customPrompt"
          placeholder="例如：特定的镜头角度、光线效果、动作要求等..."
          :rows="2"
          class="resize-none"
        />
        <p class="text-xs text-gray-500 mt-1">
          描述您对这个视频片段的特殊要求
        </p>
      </div>

      <!-- Preview Settings - Compact -->
      <div class="bg-gray-50 rounded-lg p-3">
        <h4 class="text-sm font-medium text-gray-700 mb-2">当前设置预览</h4>
        <div class="grid grid-cols-2 gap-2 text-xs">
          <div class="flex justify-between">
            <span class="text-gray-600">风格:</span>
            <span class="text-gray-900 font-medium">
              {{ getOptionLabel('style', settings.style) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">质量:</span>
            <span class="text-gray-900 font-medium">
              {{ getOptionLabel('quality', settings.quality) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">时长:</span>
            <span class="text-gray-900 font-medium">
              {{ getOptionLabel('duration', settings.duration) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">节奏:</span>
            <span class="text-gray-900 font-medium">
              {{ getOptionLabel('pacing', settings.pacing) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Shot {
  id: string
  projectId: string
  text: string
  orderIndex: number
  shotNumber: number
  imageUrl: string
  audioUrl?: string
  videoUrl?: string
  createdAt: string
  updatedAt: string
}

interface ShotData {
  shotId: string
  shot: Shot
}

interface Props {
  isOpen: boolean
  onClose: () => void
  shotData?: ShotData | null
  onGenerate: (shotId: string) => void
}

const props = defineProps<Props>()

const settings = ref({
  style: 'realistic',
  quality: 'high',
  duration: 'medium',
  pacing: 'normal',
  customPrompt: ''
})

const options = {
  style: [
    { value: 'realistic', label: '写实风格', description: '真实场景，自然光线' },
    { value: 'anime', label: '动漫风格', description: '卡通动画，色彩鲜艳' },
    { value: 'cinematic', label: '电影风格', description: '电影质感，专业拍摄' },
    { value: 'artistic', label: '艺术风格', description: '创意表现，独特视角' }
  ],
  quality: [
    { value: 'standard', label: '标准质量', description: '快速生成，适合预览' },
    { value: 'high', label: '高质量', description: '精细画质，推荐使用' },
    { value: 'ultra', label: '超高质量', description: '顶级画质，生成较慢' }
  ],
  duration: [
    { value: 'short', label: '短片段 (5-8秒)', description: '快速场景切换' },
    { value: 'medium', label: '中等 (8-12秒)', description: '标准时长' },
    { value: 'long', label: '长片段 (12-15秒)', description: '详细展示内容' }
  ],
  pacing: [
    { value: 'slow', label: '慢节奏', description: '缓慢镜头，适合展示' },
    { value: 'normal', label: '正常节奏', description: '标准播放速度' },
    { value: 'fast', label: '快节奏', description: '快速切换，动感十足' }
  ]
}

const handleSettingChange = (key: string, value: string) => {
  settings.value = { ...settings.value, [key]: value } as typeof settings.value
}

const handleGenerate = () => {
  if (props.shotData?.shotId) {
    props.onGenerate(props.shotData.shotId)
  }
  props.onClose()
}

const handleCancel = () => {
  settings.value = {
    style: 'realistic',
    quality: 'high',
    duration: 'medium',
    pacing: 'normal',
    customPrompt: ''
  }
  props.onClose()
}

const getSettingLabel = (key: string) => {
  const labels = {
    style: '视频风格',
    quality: '生成质量',
    duration: '视频时长',
    pacing: '播放节奏'
  }
  return labels[key as keyof typeof labels] || key
}

const getOptionLabel = (category: string, value: string) => {
  const categoryOptions = options[category as keyof typeof options]
  const option = categoryOptions?.find(opt => opt.value === value)
  return option?.label || value
}

// 监听模态框关闭，重置设置
watch(() => props.isOpen, (newVal) => {
  if (!newVal) {
    settings.value = {
      style: 'realistic',
      quality: 'high',
      duration: 'medium',
      pacing: 'normal',
      customPrompt: ''
    }
  }
})
</script>

<style scoped>
.video-generation-modal :deep(.ant-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.border-primary {
  border-color: #1890ff;
}

.text-primary {
  color: #1890ff;
}

.focus\:ring-primary:focus {
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}
</style> 