<template>
  <div>
    <!-- 移动端汉堡菜单按钮 -->
    <a-button
      type="primary"
      shape="circle"
      class="mobile-only fixed top-4 left-4 z-50"
      @click="mobileMenuOpen = true"
    >
      <template #icon>
        <MenuOutlined />
      </template>
    </a-button>

    <!-- 桌面端侧边栏 - 固定宽度 -->
    <nav class="hidden lg:flex w-60 bg-gray-900 text-white flex-col fixed top-0 left-0 h-screen z-30">
      <NavigationContent />
    </nav>

    <!-- 平板端侧边栏 - 收缩模式 -->
    <nav class="hidden md:flex lg:hidden w-16 bg-gray-900 text-white flex-col fixed top-0 left-0 h-screen z-30">
      <NavigationContent :isCollapsed="true" />
    </nav>

    <!-- 移动端抽屉菜单 -->
    <a-drawer
      v-model:open="mobileMenuOpen"
      title="AI视频生成平台"
      placement="left"
      :width="300"
      :body-style="{ padding: 0 }"
      :get-container="false"
      :mask="true"
    >
      <template #title>
        <div class="flex items-center gap-3 h-full">
          <img
            src="https://images.unsplash.com/photo-1501854140801-50d01698950b?w=150&h=100&fit=crop&q=80"
            alt="AI视频生成平台"
            class="w-8 h-8 rounded"
          />
          <span class="text-lg font-semibold">AI视频生成平台</span>
        </div>
      </template>
      
      <div class="bg-gray-900 text-white h-full">
        <NavigationContent />
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MenuOutlined } from '@ant-design/icons-vue'
import NavigationContent from './NavigationContent.vue'

const mobileMenuOpen = ref(false)

</script>

<style scoped>
/* 移动端专用元素 - 只在小屏幕显示 */
.mobile-only {
  display: block;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none !important;
  }
}

/* 桌面端专用元素 - 只在大屏幕显示 */
@media (max-width: 767px) {
  .desktop-only {
    display: none !important;
  }
}
</style> 