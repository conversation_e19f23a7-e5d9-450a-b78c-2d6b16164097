import ScriptItem from './ScriptItem';

const ScriptList = ({ scripts, setScripts }) => {
  if (!scripts || scripts.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed">
        <span className="material-icons text-6xl text-gray-300 mb-4 block">
          description
        </span>
        <p className="text-gray-500 text-lg">
          暂无脚本内容
        </p>
        <p className="text-gray-400 text-sm">
          点击下方的 添加新条目 来手动创建脚本
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {scripts.map((script, index) => (
        <ScriptItem
          key={script.id}
          script={script}
          index={index}
          setScripts={setScripts}
        />
      ))}
    </div>
  );
};

export default ScriptList;
