import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AppProvider } from './context/AppContext.jsx';
import { ToastContainer } from './components/common/Toast';
import { useToast } from './hooks/useToast.js';
import ProjectSelector from './components/project/ProjectSelector';
import ProjectDetail from './components/project/ProjectDetail';

// 导入 Ant Design 样式
import 'antd/dist/reset.css';

function AppContent() {
  const { toasts, removeToast } = useToast();

  return (
    <>
      <Routes>
        <Route path="/" element={<ProjectSelector />} />
        <Route path="/project/:projectId" element={<ProjectDetail />} />
      </Routes>
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </>
  );
}

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AntdApp>
      <AppProvider>
        <Router>
          <div className="App">
            <AppContent />
          </div>
        </Router>
      </AppProvider>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
