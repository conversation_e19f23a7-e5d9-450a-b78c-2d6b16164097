const { nanoid } = require('nanoid');
const { getDB } = require('../db/database');

const createProject = (req, res) => {
  const db = getDB();
  const { name = '未命名项目', theme = '', requirements = '', isDraft = false, textMaterial = '' } = req.body;
  const projectId = `proj_${nanoid()}`;
  const now = new Date().toISOString();

  try {
    const stmt = db.prepare(
      `INSERT INTO projects (id, name, theme, requirements, text_material, is_draft, created_at, updated_at)
       VALUES (@id, @name, @theme, @requirements, @text_material, @is_draft, @created_at, @updated_at)`
    );
    stmt.run({
      id: projectId,
      name,
      theme,
      requirements,
      text_material: textMaterial,
      is_draft: isDraft ? 1 : 0,
      created_at: now,
      updated_at: now,
    });
    
    const project = db.prepare('SELECT * FROM projects WHERE id = ?').get(projectId);
    console.log(`Project created in DB: ${projectId}`);
    res.status(201).json(project);
  } catch (error) {
    console.error('Failed to create project:', error);
    res.status(500).json({ error: 'Failed to create project in database' });
  }
};

const getAllProjects = (req, res) => {
  const db = getDB();
  try {
    const projects = db.prepare("SELECT * FROM projects ORDER BY updated_at DESC").all();
    const assetsStmt = db.prepare('SELECT * FROM assets WHERE project_id = ?');

    const projectsWithAssets = projects.map(p => {
      const assets = assetsStmt.all(p.id);
      return { ...p, assets: assets || [] };
    });

    res.json(projectsWithAssets);
  } catch (error) {
    console.error('Failed to get projects:', error);
    res.status(500).json({ error: 'Failed to retrieve projects' });
  }
};

const getProjectById = (req, res) => {
  const db = getDB();
  const { projectId } = req.params;
  try {
    const project = db.prepare('SELECT * FROM projects WHERE id = ?').get(projectId);
    if (project) {
      const assets = db.prepare('SELECT * FROM assets WHERE project_id = ?').all(projectId);
      project.assets = assets || [];
      res.json(project);
    } else {
      res.status(404).json({ error: 'Project not found' });
    }
  } catch (error) {
    console.error(`Failed to get project ${projectId}:`, error);
    res.status(500).json({ error: 'Failed to retrieve project' });
  }
};

const updateProject = (req, res) => {
  const db = getDB();
  const { projectId } = req.params;
  const { name, theme, requirements, textMaterial } = req.body;
  const now = new Date().toISOString();

  const updates = [];
  const params = { id: projectId, updated_at: now };

  if (name !== undefined) {
    updates.push("name = @name");
    params.name = name;
  }
  if (theme !== undefined) {
    updates.push("theme = @theme");
    params.theme = theme;
  }
  if (requirements !== undefined) {
    updates.push("requirements = @requirements");
    params.requirements = requirements;
  }
  if (textMaterial !== undefined) {
    updates.push("text_material = @textMaterial");
    params.textMaterial = textMaterial;
  }

  if (updates.length === 0) {
    return res.status(400).json({ error: "No fields to update" });
  }

  const query = `UPDATE projects SET ${updates.join(', ')}, updated_at = @updated_at WHERE id = @id`;
  const stmt = db.prepare(query);

  try {
    const info = stmt.run(params);

    if (info.changes === 0) {
      return res.status(404).json({ error: "Project not found, nothing updated." });
    }
    
    const updatedProject = getProjectByIdForUpdate(projectId);
    res.status(200).json(updatedProject);

  } catch (error) {
    console.error(`Failed to update project ${projectId}:`, error);
    res.status(500).json({ error: 'Failed to update project' });
  }
};

// Helper function to get a project with its assets. Used after an update.
const getProjectByIdForUpdate = (projectId) => {
  const db = getDB();
  const project = db.prepare('SELECT * FROM projects WHERE id = ?').get(projectId);
  if (project) {
    const assets = db.prepare('SELECT * FROM assets WHERE project_id = ?').all(projectId);
    project.assets = assets || [];
  }
  return project;
};

const deleteProject = (req, res) => {
  const db = getDB();
  const { projectId } = req.params;
  try {
    const info = db.prepare('DELETE FROM projects WHERE id = ?').run(projectId);
    if (info.changes > 0) {
      console.log(`Project deleted from DB: ${projectId}`);
      res.json({ success: true, message: 'Project deleted successfully' });
    } else {
      res.status(404).json({ message: 'Project not found' });
    }
  } catch (error) {
    console.error(`Failed to delete project ${projectId}:`, error);
    res.status(500).json({ message: 'Failed to delete project' });
  }
};

const uploadAsset = (req, res) => {
  const db = getDB();
  const { projectId } = req.params;
  
  try {
    const project = db.prepare('SELECT id FROM projects WHERE id = ?').get(projectId);
    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded.' });
    }

    const fileInfo = {
      id: req.file.filename,
      project_id: projectId,
      original_name: req.file.originalname,
      filename: req.file.filename,
      mimetype: req.file.mimetype,
      size: req.file.size,
      url: `/uploads/${req.file.filename}`,
      uploaded_at: new Date().toISOString(),
    };

    const stmt = db.prepare(
        `INSERT INTO assets (id, project_id, original_name, filename, mimetype, size, url, uploaded_at)
         VALUES (@id, @project_id, @original_name, @filename, @mimetype, @size, @url, @uploaded_at)`
    );
    stmt.run(fileInfo);

    console.log(`Asset link to project ${projectId} in DB: ${fileInfo.original_name}`);
    res.json({ success: true, message: 'File uploaded and linked successfully', ...fileInfo });
  } catch (error) {
    console.error(`Failed to upload asset for project ${projectId}:`, error);
    res.status(500).json({ message: 'Failed to upload asset' });
  }
};

const getProjectAssets = (req, res) => {
  const db = getDB();
  const { projectId } = req.params;
  try {
    const assets = db.prepare('SELECT * FROM assets WHERE project_id = ?').all(projectId);
    res.json({ projectId, assets, count: assets.length });
  } catch (error) {
    console.error(`Failed to get assets for project ${projectId}:`, error);
    res.status(500).json({ message: 'Failed to get project assets' });
  }
};

const deleteAsset = (req, res) => {
    const db = getDB();
    const { projectId, assetId } = req.params;
    try {
        const asset = db.prepare('SELECT * FROM assets WHERE id = ? AND project_id = ?').get(assetId, projectId);
        if (!asset) {
            return res.status(404).json({ message: 'Asset not found' });
        }
        
        // You might want to delete the physical file as well
        // fs.unlinkSync(path.join(__dirname, '../../uploads', asset.filename));

        const info = db.prepare('DELETE FROM assets WHERE id = ?').run(assetId);
        if (info.changes > 0) {
            console.log(`Asset deleted from DB: ${asset.original_name}`);
            res.json({ success: true, message: 'Asset deleted successfully', deletedAsset: asset });
        } else {
             res.status(404).json({ message: 'Asset not found during deletion' });
        }
    } catch (error) {
        console.error(`Failed to delete asset ${assetId}:`, error);
        res.status(500).json({ message: 'Failed to delete asset' });
    }
};

module.exports = {
  createProject,
  getAllProjects,
  getProjectById,
  updateProject,
  deleteProject,
  uploadAsset,
  getProjectAssets,
  deleteAsset,
};
