<template>
  <div class="bg-white rounded-lg p-6 shadow-sm">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">合成设置</h3>
    
    <div class="space-y-4">
      <!-- 质量设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">视频质量</label>
        <a-select v-model:value="settings.quality" class="w-full">
          <a-select-option value="720p">720p (HD)</a-select-option>
          <a-select-option value="1080p">1080p (Full HD)</a-select-option>
          <a-select-option value="4K">4K (Ultra HD)</a-select-option>
        </a-select>
      </div>

      <!-- 格式设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">输出格式</label>
        <a-select v-model:value="settings.format" class="w-full">
          <a-select-option value="MP4">MP4</a-select-option>
          <a-select-option value="MOV">MOV</a-select-option>
          <a-select-option value="AVI">AVI</a-select-option>
        </a-select>
      </div>

      <!-- 帧率设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">帧率</label>
        <a-select v-model:value="settings.frameRate" class="w-full">
          <a-select-option value="24">24fps</a-select-option>
          <a-select-option value="30">30fps</a-select-option>
          <a-select-option value="60">60fps</a-select-option>
        </a-select>
      </div>

      <!-- 音频设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">音频质量</label>
        <a-select v-model:value="settings.audioQuality" class="w-full">
          <a-select-option value="128">128kbps</a-select-option>
          <a-select-option value="192">192kbps</a-select-option>
          <a-select-option value="320">320kbps</a-select-option>
        </a-select>
      </div>

      <!-- 动作按钮 -->
      <div class="pt-4 border-t border-gray-200">
        <div class="flex gap-2">
          <a-button
            type="primary"
            @click="handleRecompose"
            :disabled="isRecomposing"
            class="flex-1"
          >
            <template v-if="isRecomposing">
              <div class="inline-flex items-center">
                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                <span>重新合成中...</span>
              </div>
            </template>
            <template v-else>
              <ReloadOutlined />
              重新合成
            </template>
          </a-button>
          <a-button @click="handleReset">
            <UndoOutlined />
            重置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 设置说明 -->
    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
      <div class="flex items-start gap-3">
        <InfoCircleOutlined class="text-blue-500 mt-0.5" />
        <div>
          <h4 class="text-sm font-medium text-blue-900 mb-1">设置说明</h4>
          <p class="text-sm text-blue-700">
            修改设置后需要重新合成才能生效。更高的质量设置会增加处理时间。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ReloadOutlined, UndoOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'

interface Settings {
  quality: string
  format: string
  frameRate: string
  audioQuality: string
}

interface Props {
  onRecompose: () => void
}

const props = defineProps<Props>()

const isRecomposing = ref(false)

const settings = reactive<Settings>({
  quality: '1080p',
  format: 'MP4',
  frameRate: '30',
  audioQuality: '192'
})

const defaultSettings: Settings = {
  quality: '1080p',
  format: 'MP4',
  frameRate: '30',
  audioQuality: '192'
}

const handleRecompose = async () => {
  isRecomposing.value = true
  
  // 延迟执行以显示加载状态
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  props.onRecompose()
  isRecomposing.value = false
}

const handleReset = () => {
  Object.assign(settings, defaultSettings)
}
</script> 