<template>
  <div class="min-h-screen bg-gray-100">
    <Sidebar />

    <!-- 主内容区域 - 响应式左边距以适配固定侧边栏 -->
    <main class="min-h-screen flex flex-col transition-all duration-300 ml-0 md:ml-16 lg:ml-60">
      <StatusBar />

      <div class="flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto pb-20 md:pb-8">
        <div class="animate-fade-in">
          <component :is="currentTabComponent" />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useNavigation } from '../../composables/useNavigation'
import Sidebar from './Sidebar.vue'
import StatusBar from './StatusBar.vue'

// Import tab components
import UploadContent from '../upload/UploadContent.vue'
import ScriptGeneration from '../script/ScriptGeneration.vue'
import StoryboardEditor from '../storyboard/StoryboardEditor.vue'
import VoiceGeneration from '../voice-generation/VoiceGeneration.vue'
import VideoGeneration from '../video-generation/VideoGeneration.vue'
import AvatarConfiguration from '../avatar/AvatarConfiguration.vue'
import VideoCompose from '../video-compose/VideoCompose.vue'
import VideoExport from '../export/VideoExport.vue'

const { currentTab } = useNavigation()

const tabComponents = {
  upload: UploadContent,
  script: ScriptGeneration,
  storyboard: StoryboardEditor,
  voice: VoiceGeneration,
  video: VideoGeneration,
  avatar: AvatarConfiguration,
  compose: VideoCompose,
  export: VideoExport
}

const currentTabComponent = computed(() => {
  return tabComponents[currentTab.value as keyof typeof tabComponents] || UploadContent
})
</script>

<style>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* 确保主内容区域在不同屏幕尺寸下的正确布局 */
@media (max-width: 767px) {
  /* 移动端：无左边距，使用底部导航 */
  main {
    margin-left: 0 !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  /* 平板端：为收缩侧边栏预留 64px 空间 */
  main {
    margin-left: 4rem; /* 64px */
  }
}

@media (min-width: 1024px) {
  /* 桌面端：为完整侧边栏预留 240px 空间 */
  main {
    margin-left: 15rem; /* 240px */
  }
}
</style> 