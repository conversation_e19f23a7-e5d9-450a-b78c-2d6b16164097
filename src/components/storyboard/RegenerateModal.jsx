import { useState } from 'react';
import Modal from '../common/Modal';
import Button from '../common/Button';
import { App } from 'antd';

const RegenerateModal = ({ isOpen, onClose, shot, onRegenerate, isProcessing }) => {
  const [prompt, setPrompt] = useState('');
  const { modal } = App.useApp();

  const handleConfirm = () => {
    modal.confirm({
      title: '确认重新生成镜头?',
      content: '重新生成后当前镜头图片将被替换，确定要继续吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        onRegenerate(shot.id, prompt);
        setPrompt(''); // Reset prompt after submission
      }
    });
  };

  const handleCancel = () => {
    setPrompt('');
    onClose();
  };

  if (!shot) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title="重新生成镜头"
      footer={
        <>
          <Button variant="secondary" onClick={handleCancel} disabled={isProcessing}>
            取消
          </Button>
          <Button variant="primary" onClick={handleConfirm} loading={isProcessing}>
            {isProcessing ? '生成中...' : '确认生成'}
          </Button>
        </>
      }
    >
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            当前图片
          </label>
          <div className="w-full">
            <img
              src={shot.image_url}
              alt="当前镜头图片"
              className="w-full h-auto rounded-lg border border-gray-200"
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            原始脚本
          </label>
          <div className="bg-gray-50 p-3 rounded-md border border-gray-200 text-sm text-gray-600 max-h-24 overflow-y-auto">
            {shot.script_text}
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            输入修改建议 (可选)
          </label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="例如：改成卡通风格、让画面更明亮、增加一些科技元素..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-vertical"
          />
        </div>
      </div>
    </Modal>
  );
};

export default RegenerateModal;
