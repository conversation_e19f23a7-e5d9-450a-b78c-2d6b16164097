import { message } from 'ant-design-vue'

interface ToastOptions {
  duration?: number
  type?: 'success' | 'error' | 'warning' | 'info'
}

export function useToast() {
  const showToast = (content: string, options: ToastOptions = {}) => {
    const { type = 'info', duration = 3 } = options
    
    switch (type) {
      case 'success':
        message.success(content, duration)
        break
      case 'error':
        message.error(content, duration)
        break
      case 'warning':
        message.warning(content, duration)
        break
      case 'info':
      default:
        message.info(content, duration)
        break
    }
  }

  const success = (content: string, duration = 3) => {
    message.success(content, duration)
  }

  const error = (content: string, duration = 3) => {
    message.error(content, duration)
  }

  const warning = (content: string, duration = 3) => {
    message.warning(content, duration)
  }

  const info = (content: string, duration = 3) => {
    message.info(content, duration)
  }

  return {
    showToast,
    success,
    error,
    warning,
    info
  }
} 