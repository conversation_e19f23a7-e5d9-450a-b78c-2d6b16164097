package com.example.config.web;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Slf4j
@Configuration
public class StaticResourceConfig implements WebMvcConfigurer {

    @Value("${app.upload-path:./uploads}")
    private String uploadPath;

    @Value("${app.static-path:./static}")
    private String staticPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        String[][] pathMappings = {
            {staticPath, "/static/**", "static path"},
            {uploadPath, "/uploads/**", "upload path"}
        };

        for (String[] mapping : pathMappings) {
            String path = mapping[0];
            String pattern = mapping[1];
            String description = mapping[2];
            
            log.info("serve {} : {} to {}", description, path, pattern);
            registry.addResourceHandler(pattern)
                    .addResourceLocations("file:" + path)
                    .setCachePeriod(3600);
        }

    }
}
