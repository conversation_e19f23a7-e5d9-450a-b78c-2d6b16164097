package com.example.common.page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageRequest {

    private int page = 0;

    private int size = 10;

    private String sort = "id";

    private String order = "desc";


    public Pageable getPageable() {
        // 校正参数
        int safePage = Math.max(0, page - 1);
        int safeSize = Math.max(1, size);

        String safeSort = StringUtils.hasText(sort) ? sort : "id";
        Sort.Direction direction = "asc".equalsIgnoreCase(order) ? Sort.Direction.ASC : Sort.Direction.DESC;

        return org.springframework.data.domain.PageRequest.of(safePage, safeSize, Sort.by(direction, safeSort));
    }


}
