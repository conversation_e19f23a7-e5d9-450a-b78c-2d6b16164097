package com.example.param.script;

import lombok.Data;
import jakarta.validation.constraints.NotNull;

/**
 * 脚本插入参数
 */
@Data
public class ScriptInsertParam {

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private String projectId;

    /**
     * 脚本文本内容
     */
    private String text;

    /**
     * 插入位置的索引（从0开始）
     * 如果为null，则添加到最后
     */
    private Integer insertIndex;
} 