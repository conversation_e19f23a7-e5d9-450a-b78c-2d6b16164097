import { Radio } from 'antd';

const MaterialSourceSelector = ({ source, setSource }) => {
  const handleSourceChange = (e) => {
    setSource(e.target.value);
  };

  const options = [
    { label: '上传文件', value: 'upload' },
    { label: '输入文本', value: 'text' },
  ];

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        素材来源
      </label>
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 w-fit">
        <Radio.Group
          options={options}
          onChange={handleSourceChange}
          value={source}
          optionType="button"
          buttonStyle="solid"
          size="large"
        />
      </div>
    </div>
  );
};

export default MaterialSourceSelector;
