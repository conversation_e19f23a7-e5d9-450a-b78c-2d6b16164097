<template>
  <div>
    <!-- Loading State -->
    <template v-if="isLoadingShots">
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 mb-2">
          生成配音
        </h2>
        <p class="text-gray-600">
          为每个镜头选择合适的配音类型，AI将生成专业的配音效果
        </p>
      </div>

      <div class="text-center py-12">
        <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto mb-4"></div>
        <p class="text-gray-500 text-lg">
          正在加载项目镜头数据...
        </p>
      </div>
    </template>

    <!-- Empty State -->
    <template v-else-if="shots.length === 0">
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 mb-2">
          生成配音
        </h2>
        <p class="text-gray-600">
          为每个镜头选择合适的配音类型，AI将生成专业的配音效果
        </p>
      </div>

      <div class="text-center py-12">
        <MicNoneIcon class="text-6xl text-gray-300 mb-4 block mx-auto" />
        <p class="text-gray-500 text-lg">
          请先在"镜头编辑"页面创建镜头，才能生成对应的配音。
        </p>
        <a-button
          v-if="!isLoadingShots && appStore.projectId"
          type="default"
          @click="() => loadShotsData(appStore.projectId!)"
          class="mt-4"
        >
          重新加载镜头数据
        </a-button>
      </div>
    </template>

    <!-- Main Content -->
    <template v-else>
      <!-- Header -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 mb-2">
          生成配音
        </h2>
        <p class="text-gray-600">
          为每个镜头选择合适的配音类型，AI将生成专业的配音效果
        </p>
        <p v-if="isLoadingVoices" class="text-blue-500 text-sm mt-2">
          🔊 正在检查配音服务连接...
        </p>
      </div>

      <!-- Voice Cards Grid -->
      <div class="space-y-4 mb-8">
        <VoiceCard
          v-for="(shot, index) in shots"
          :key="shot.id"
          :shot="shot"
          :index="index"
          :voice-settings="shotVoices[shot.id] || {}"
          :available-voices="availableVoices"
          :is-generating="generatingShots.has(shot.id)"
          :on-voice-select="(voiceType) => handleVoiceSelect(shot.id, voiceType)"
          :on-generate="() => handleGenerateVoice(shot.id)"
          :on-customize="() => openModal({ shotId: shot.id, shot })"
        />
      </div>

      <!-- Batch Actions -->
      <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              批量操作
            </h3>
            <p class="text-gray-600">
              为所有镜头快速生成配音，或统一设置配音类型
            </p>
          </div>
          <div class="flex gap-3">
            <a-button
              type="primary"
              @click="handleGenerateAll"
              :disabled="isGenerating"
            >
              <template v-if="isGenerating">
                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                正在生成全部配音...
              </template>
              <template v-else>
                生成全部配音
              </template>
            </a-button>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div v-if="allVoicesGenerated" class="flex justify-end">
        <a-button
          type="primary"
          @click="handleContinue"
        >
          确认配音并生成视频
        </a-button>
      </div>
    </template>

    <!-- Voice Generation Modal -->
    <VoiceGenerationModal
      :is-open="isModalOpen"
      :on-close="closeModal"
      :shot-data="modalData"
      :on-generate="handleGenerateVoice"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { AudioOutlined as MicNoneIcon } from '@ant-design/icons-vue'
import { useAppStore } from '../../stores/app'
import { useNavigation } from '../../composables/useNavigation'
import { useApi } from '../../composables/useApi'
import VoiceCard from './VoiceCard.vue'
import VoiceGenerationModal from './VoiceGenerationModal.vue'

interface Shot {
  id: string
  imageUrl: string
  text: string
  audioUrl?: string
}

interface VoiceSettings {
  voiceType?: string
  isGenerated?: boolean
  audioUrl?: string
  duration?: number
  speed?: number
  pitch?: number
  emotion?: string
}

interface Voice {
  id: string
  name: string
  description: string
  icon: string
  previewUrl?: string
}

interface ModalData {
  shotId: string
  shot: Shot
}

const appStore = useAppStore()
const { setCurrentTab } = useNavigation()
const { getShots, getVoices, generateVoice, generateBatchVoice } = useApi()

// 状态管理
const shots = ref<Shot[]>([])
const shotVoices = ref<Record<string, VoiceSettings>>({})
const isGenerating = ref(false)
const generatingShots = ref(new Set<string>())
const isLoadingShots = ref(false)
const isLoadingVoices = ref(false)
const availableVoices = ref<Voice[]>([])

// 模态框状态
const isModalOpen = ref(false)
const modalData = ref<ModalData | null>(null)

// 加载镜头数据
const loadShotsData = async (projectId: string) => {
  if (!projectId) return
  
  isLoadingShots.value = true
  try {
    const data = await getShots(projectId)
    
    shots.value = data.map((shot: any) => ({
      ...shot,
      imageUrl: shot.imageUrl,
      audioUrl: shot.audioUrl
    }))
    
    // 为每个镜头初始化配音设置，检查是否已有音频
    shots.value.forEach((shot: Shot) => {
      if (!shotVoices.value[shot.id]) {
        // 检查是否已经存在audioUrl来判断是否已生成音频
        const hasAudio = !!(shot.audioUrl && shot.audioUrl.trim() !== '')
        shotVoices.value[shot.id] = {
          voiceType: 'professional-female',
          isGenerated: hasAudio,
          audioUrl: hasAudio ? shot.audioUrl : undefined,
          speed: 1.0,
          pitch: 1.0,
          emotion: 'neutral'
        }
      }
    })
  } catch (error) {
    console.error('❌ 加载镜头数据失败:', error)
    message.error('加载镜头数据失败')
  } finally {
    isLoadingShots.value = false
  }
}

// 加载配音类型
const loadVoicesAPI = async () => {
  isLoadingVoices.value = true
  try {
    const data = await getVoices()
    availableVoices.value = data
  } catch (error) {
    console.error('❌ 配音类型API连接失败，使用内置配音类型:', error)
    // 使用内置配音类型
    availableVoices.value = [
      {
        id: 'professional-female',
        name: '专业女声',
        description: '清晰专业，适合商务解说',
        icon: '👩‍💼'
      },
      {
        id: 'professional-male',
        name: '专业男声',
        description: '沉稳可靠，适合新闻播报',
        icon: '👨‍💼'
      },
      {
        id: 'friendly-female',
        name: '亲切女声',
        description: '温暖友好，适合教学内容',
        icon: '😊'
      },
      {
        id: 'friendly-male',
        name: '亲切男声',
        description: '平易近人，适合日常解说',
        icon: '🙂'
      }
    ]
  } finally {
    isLoadingVoices.value = false
  }
}

// 选择配音类型
const handleVoiceSelect = (shotId: string, voiceType: string) => {
  shotVoices.value = {
    ...shotVoices.value,
    [shotId]: {
      ...shotVoices.value[shotId],
      voiceType,
      isGenerated: false,
      audioUrl: undefined
    }
  }
}

// 生成单个配音
const handleGenerateVoice = async (shotId: string) => {
  generatingShots.value = new Set([...generatingShots.value, shotId])

  try {
    // 使用ApiService调用配音生成API
    const shot = shots.value.find(s => s.id === shotId)
    const voiceSettings = shotVoices.value[shotId]
    
    console.log('🎵 开始生成配音:', { shotId, voiceSettings, text: shot?.text })
    
    // 准备配音数据
    const voiceData = {
      id: shotId,
      voiceType: voiceSettings?.voiceType || 'professional-female',
      speed: voiceSettings?.speed || 1.0,
      pitch: voiceSettings?.pitch || 1.0,
      emotion: voiceSettings?.emotion || 'neutral'
    }
    
    // 调用真实API
    const result = await generateVoice(voiceData);
    console.log('✅ 配音生成成功:', result)
    
    // 使用后端返回的真实数据更新状态
    shotVoices.value = {
      ...shotVoices.value,
      [shotId]: {
        ...shotVoices.value[shotId],
        isGenerated: true,
        audioUrl: result.audioUrl,
      }
    }
    
    // 移除生成状态
    const newSet = new Set(generatingShots.value)
    newSet.delete(shotId)
    generatingShots.value = newSet
    
    message.success('配音生成成功')
  } catch (error) {
    console.error('❌ 配音生成失败:', error)
    message.error('配音生成失败')
    
    const newSet = new Set(generatingShots.value)
    newSet.delete(shotId)
    generatingShots.value = newSet
  }
}

// 批量生成配音
const handleGenerateAll = async () => {
  isGenerating.value = true

  try {
    console.log('🎵 开始批量生成配音')
    
    // 为所有镜头逐个生成配音
    const promises = shots.value.map(async (shot) => {
      const voiceSettings = shotVoices.value[shot.id]
      const voiceData = {
        id: shot.id,
        voiceType: voiceSettings?.voiceType || 'professional-female',
        speed: voiceSettings?.speed || 1.0,
        pitch: voiceSettings?.pitch || 1.0,
        emotion: voiceSettings?.emotion || 'neutral'
      }
      
      try {
        const result = await generateVoice(voiceData)
        shotVoices.value = {
          ...shotVoices.value,
          [shot.id]: {
            ...shotVoices.value[shot.id],
            isGenerated: true,
            audioUrl: result.audioUrl,
            duration: Math.floor(Math.random() * 15) + 10
          }
        }
        console.log(`✅ 镜头 ${shot.id} 配音生成成功`)
      } catch (error) {
        console.error(`❌ 镜头 ${shot.id} 配音生成失败:`, error)
        throw error
      }
    })

    await Promise.all(promises)
    message.success('全部配音生成完成')
  } catch (error) {
    console.error('❌ 批量配音生成失败:', error)
    message.error('批量配音生成失败')
  } finally {
    isGenerating.value = false
  }
}

// 继续到下一步
const handleContinue = () => {
  setCurrentTab('video')
}

// 打开模态框
const openModal = (data: ModalData) => {
  modalData.value = data
  isModalOpen.value = true
}

// 关闭模态框
const closeModal = () => {
  modalData.value = null
  isModalOpen.value = false
}

// 计算是否所有配音都已生成
const allVoicesGenerated = computed(() => {
  return shots.value.every(shot =>
    shotVoices.value[shot.id]?.isGenerated
  )
})

// 组件挂载时的初始化
onMounted(() => {
  loadVoicesAPI()
})

// 监听项目ID变化
watch(() => appStore.projectId, (newProjectId) => {
  if (newProjectId) {
    loadShotsData(newProjectId)
  }
}, { immediate: true })
</script>

<style>
.border-t-primary {
  border-top-color: #1890ff;
}
</style> 