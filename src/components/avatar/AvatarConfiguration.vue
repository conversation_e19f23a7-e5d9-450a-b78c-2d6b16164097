<template>
  <div>
    <!-- Header -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold text-gray-900 mb-2">
        添加数字人
      </h2>
      <p class="text-gray-600">
        为每个镜头选择和配置数字人形象，让您的视频更加生动和专业
      </p>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoadingShots || isLoadingAvatars">
      <div class="text-center py-12">
        <div class="w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
        <p class="text-gray-500 text-lg">正在加载资源...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error">
      <div class="text-center py-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
          <span class="material-icons text-2xl text-red-500">error_outline</span>
        </div>
        <p class="text-red-500 text-lg mb-4">{{ error }}</p>
        <a-button type="primary" @click="loadShotsData">重试</a-button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="shots.length === 0">
      <div class="text-center py-12">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
          <span class="material-icons text-2xl text-gray-300">person_outline</span>
        </div>
        <p class="text-gray-500 text-lg">
          请先在"镜头编辑"页面创建镜头，才能添加对应的数字人。
        </p>
      </div>
    </div>

    <!-- 内容区域 -->
    <div v-else>
      <!-- Avatar Cards Grid -->
      <div class="grid grid-cols-1 gap-6 mb-8">
        <AvatarCard
          v-for="(shot, index) in shots"
          :key="shot.id"
          :shot="shot"
          :index="index"
          :avatar-settings="shotAvatars[shot.id] || {}"
          :is-configuring="configuringShots.has(shot.id)"
          :on-avatar-select="(avatarType) => handleAvatarSelect(shot.id, avatarType)"
          :on-configure="() => handleConfigureAvatar(shot.id)"
          :on-customize="() => openModal({ shotId: shot.id.toString(), shot })"
          :avatar-options="avatarOptions"
        />
      </div>

      <!-- Batch Actions -->
      <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              批量操作
            </h3>
            <p class="text-gray-600">
              为所有镜头快速配置数字人，或统一设置数字人类型
            </p>
          </div>
          <div class="flex gap-3">
            <a-button
              @click="handleSkipAll"
              :disabled="isConfiguring"
            >
              全部跳过
            </a-button>
            <a-button
              type="primary"
              @click="handleConfigureAll"
              :disabled="isConfiguring"
            >
              <template v-if="isConfiguring">
                <div class="inline-flex items-center">
                  <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  <span>正在配置全部数字人...</span>
                </div>
              </template>
              <template v-else>
                配置全部数字人
              </template>
            </a-button>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-between">
        <a-button
          type="default"
          @click="handleSkipDigitalHuman"
          size="large"
        >
          跳过数字人设置
        </a-button>
        <a-button
          v-if="allShotsProcessed"
          type="primary"
          @click="handleContinue"
          size="large"
        >
          确认设置并进入视频合成
        </a-button>
      </div>
    </div>

    <!-- Avatar Configuration Modal -->
    <AvatarConfigurationModal
      :is-open="isModalOpen"
      :shot-data="modalData"
      :on-close="closeModal"
      :on-configure="handleConfigureAvatar"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAppStore } from '../../stores/app'
import { useNavigation } from '../../composables/useNavigation'
import { useApi } from '../../composables/useApi'
import AvatarCard from './AvatarCard.vue'
import AvatarConfigurationModal from './AvatarConfigurationModal.vue'
import { message } from 'ant-design-vue'

interface AvatarOption {
  id: string;
  name: string;
  previewUrl: string;
}

interface AvatarSettings {
  avatarType?: string
  position?: string
  size?: string
  background?: string
  lighting?: string
  expression?: string
  isConfigured?: boolean
  previewUrl?: string | null
}

interface ShotAvatars {
  [key: string]: AvatarSettings
}

interface ModalData {
  shotId: string
  shot: any
}

const appStore = useAppStore()
const { setCurrentTab } = useNavigation()
const { getShots, generateAvatarVideo, getAvatars, clearAvatar } = useApi()

// State
const shotAvatars = ref<ShotAvatars>({})
const isConfiguring = ref(false)
const configuringShots = ref<Set<string>>(new Set())
const isModalOpen = ref(false)
const modalData = ref<ModalData | undefined>(undefined)
const isLoadingShots = ref(false)
const isLoadingAvatars = ref(false)
const shots = ref<any[]>([])
const error = ref<string | null>(null)
const avatarOptions = ref<any[]>([])

// Computed
const allAvatarsConfigured = computed(() => {
  return shots.value.every(shot =>
    shotAvatars.value[shot.id]?.isConfigured
  )
})

// 计算是否所有镜头都已处理（配置或跳过）
const allShotsProcessed = computed(() => {
  return shots.value.every(shot => {
    const avatarSetting = shotAvatars.value[shot.id]
    return avatarSetting?.isConfigured || avatarSetting?.avatarType === 'none'
  })
})

// Methods
const loadShotsData = async () => {
  if (!appStore.projectId) {
    error.value = '请先选择项目'
    return
  }

  isLoadingShots.value = true
  error.value = null

  try {
    const response = await getShots(appStore.projectId)
    shots.value = response
    initializeAvatars()

  } catch (err) {
    error.value = '获取镜头数据时发生错误'
    console.error('Error loading shots:', err)
  } finally {
    isLoadingShots.value = false
  }
}

const loadAvatarOptions = async () => {
  isLoadingAvatars.value = true
  try {
    const response = await getAvatars()
    avatarOptions.value = response.map((avatar: AvatarOption) => ({
      value: avatar.id,
      label: avatar.name,
      icon: '👤'
    }))
  } catch (err) {
    error.value = '获取数字人列表时发生错误'
    console.error('Error loading avatar options:', err)
  } finally {
    isLoadingAvatars.value = false
  }
}

const initializeAvatars = () => {
  if (shots.value.length > 0) {
    const initialAvatars: ShotAvatars = {}
    shots.value.forEach(shot => {
      if (!shotAvatars.value[shot.id]) {
        initialAvatars[shot.id] = {
          avatarType: shot.avatarId || 'none',
          position: 'center',
          size: 'medium',
          background: 'transparent',
          lighting: 'natural',
          expression: 'neutral',
          isConfigured: !!shot.videoUrl,
          previewUrl: shot.videoUrl || null
        }
      }
    })
    shotAvatars.value = { ...shotAvatars.value, ...initialAvatars }
  }
}

const handleAvatarSelect = async (shotId: string, avatarType: string) => {
  const shot = shots.value.find(s => s.id === shotId)
  if (!shot) return

  // If a video exists and user selects 'none', call clear endpoint.
  if (shot.videoUrl && avatarType === 'none') {
    configuringShots.value.add(shotId)
    try {
      const updatedShot = await clearAvatar(shotId)
      // Update data with response from backend
      const index = shots.value.findIndex(s => s.id === shotId)
      if (index !== -1) shots.value[index] = updatedShot
      
      shotAvatars.value[shotId] = {
        ...shotAvatars.value[shotId],
        avatarType: 'none',
        isConfigured: false,
        previewUrl: null,
      }
      message.success('数字人配置已清除')
    } catch (err) {
      message.error('清除配置失败')
    } finally {
      configuringShots.value.delete(shotId)
    }
  } else {
    // Just update local state, and clear video if avatar type changes
    if (shot.videoUrl && shot.avatarId !== avatarType) {
        shot.videoUrl = null 
    }
    shotAvatars.value[shotId] = {
        ...shotAvatars.value[shotId],
        avatarType: avatarType,
        isConfigured: false
    }
  }
}

const handleConfigureAvatar = async (shotId: number) => {
  const avatarType = shotAvatars.value[shotId.toString()]?.avatarType
  if (!avatarType || avatarType === 'none') {
    message.error('请先为该镜头选择一个数字人类型')
    return
  }

  configuringShots.value.add(shotId.toString())
  
  try {
    const updatedShot = await generateAvatarVideo(shotId.toString(), avatarType)
    
    // 更新镜头数据和配置状态
    const index = shots.value.findIndex(s => s.id === shotId)
    if (index !== -1) {
      shots.value[index] = updatedShot
    }
    shotAvatars.value[shotId.toString()] = {
      ...shotAvatars.value[shotId.toString()],
      isConfigured: true,
      previewUrl: updatedShot.videoUrl // 使用视频URL作为预览
    }
    
  } catch (err) {
    message.error('数字人视频生成失败，请重试')
    console.error(`Error generating avatar for shot ${shotId}:`, err)
  } finally {
    configuringShots.value.delete(shotId.toString())
  }
}

const handleConfigureAll = async () => {
  isConfiguring.value = true
  const shotsToConfigure = shots.value.filter(shot => {
    const avatarType = shotAvatars.value[shot.id]?.avatarType
    return avatarType && avatarType !== 'none'
  })

  if (shotsToConfigure.length === 0) {
    message.warn('没有已选择数字人类型的镜头可供配置')
    isConfiguring.value = false
    return
  }
  
  // 为所有已选择类型的镜头配置数字人
  const promises = shotsToConfigure.map(shot => {
    configuringShots.value.add(shot.id)
    const avatarType = shotAvatars.value[shot.id]?.avatarType
    return generateAvatarVideo(shot.id, avatarType!)
      .then(updatedShot => {
        const index = shots.value.findIndex(s => s.id === shot.id)
        if (index !== -1) {
          shots.value[index] = updatedShot
        }
        shotAvatars.value[shot.id] = {
          ...shotAvatars.value[shot.id],
          isConfigured: true,
          previewUrl: updatedShot.videoUrl
        }
      })
      .catch(err => {
        message.error(`镜头 ${shot.id} 的数字人视频生成失败`)
        console.error(`Error generating avatar for shot ${shot.id}:`, err)
      })
      .finally(() => {
        configuringShots.value.delete(shot.id)
      })
  })

  await Promise.all(promises)
  isConfiguring.value = false
}

const handleSkipAll = () => {
  // 将所有镜头设置为不使用数字人
  shots.value.forEach(shot => {
    shotAvatars.value[shot.id] = {
      ...shotAvatars.value[shot.id],
      avatarType: 'none',
      isConfigured: false,
      previewUrl: null
    }
  })
}

const handleSkipDigitalHuman = () => {
  // 直接跳过数字人设置，进入下一步
  setCurrentTab('compose')
}

const handleContinue = () => {
  setCurrentTab('compose')
}

const openModal = (data: ModalData) => {
  modalData.value = data
  isModalOpen.value = true
}

const closeModal = () => {
  isModalOpen.value = false
  modalData.value = undefined
}

// Lifecycle
onMounted(() => {
  loadShotsData()
  loadAvatarOptions()
})

// Watch for project changes
watch(() => appStore.projectId, () => {
  if (appStore.projectId) {
    loadShotsData()
  }
})
</script>

<style scoped>
.material-icons {
  font-family: 'Material Icons';
}
</style> 