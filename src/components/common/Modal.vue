<template>
  <a-modal
    :open="visible"
    :title="title"
    :width="getWidth"
    :closable="closable"
    :footer="showFooter ? undefined : null"
    @cancel="handleClose"
  >
    <template v-if="$slots.title" #title>
      <slot name="title" />
    </template>

    <div :class="contentClasses">
      <slot />
    </div>

    <template v-if="showFooter" #footer>
      <slot name="footer" />
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  visible: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  closable?: boolean
  showHeader?: boolean
  showFooter?: boolean
  contentPadding?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
  showHeader: true,
  showFooter: false,
  contentPadding: true
})

const emit = defineEmits<{
  close: []
}>()

const getWidth = computed(() => {
  const sizeMap = {
    sm: 400,
    md: 520,
    lg: 800,
    xl: 1000
  }
  return sizeMap[props.size]
})

const contentClasses = computed(() => {
  const classes = []
  if (!props.contentPadding) {
    classes.push('no-padding')
  }
  return classes.join(' ')
})

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.no-padding :deep(.ant-modal-body) {
  padding: 0;
}
</style> 