<template>
  <div class="bg-white p-3 rounded-lg border border-gray-100 mb-3 flex items-start space-x-3">
    <div class="text-xs text-gray-400 bg-gray-50 rounded w-6 h-6 flex items-center justify-center flex-shrink-0 mt-1">
      {{ index + 1 }}
    </div>
    
    <a-textarea
      ref="textareaRef"
      v-model:value="currentText"
      @blur="handleSave"
      :auto-size="{ minRows: 1 }"
      placeholder="请输入脚本内容..."
      :disabled="isSaving || isDeleting || isRegenerating"
      :bordered="false"
      class="flex-1 resize-none text-sm"
    />
    
    <div class="flex space-x-1">
      <a-button
        type="text"
        size="small"
        @click="handleInsertBefore"
        :disabled="isDeleting || isSaving"
        title="在此前插入脚本"
      >
        <template #icon>
          <PlusOutlined class="text-xs" />
        </template>
      </a-button>
      
      <a-button
        v-if="!script.isNew"
        type="text"
        size="small"
        @click="handleRegenerate"
        :loading="isRegenerating"
        :disabled="isRegenerating || isSaving || isDeleting"
        title="重新生成"
      >
        <template #icon>
          <ReloadOutlined class="text-xs" />
        </template>
      </a-button>
      
      <a-button
        type="text"
        size="small"
        danger
        @click="handleDelete"
        :disabled="isDeleting || isSaving"
        title="删除"
      >
        <template #icon>
          <DeleteOutlined class="text-xs" />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { ReloadOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { useApi } from '../../composables/useApi'
import { useRoute } from 'vue-router'

interface Script {
  id: string
  text: string
  isNew?: boolean
  order_index: number
}

interface Props {
  script: Script
  index: number
}

interface Emits {
  (e: 'update-scripts', scripts: Script[]): void
  (e: 'get-scripts'): Script[]
  (e: 'refresh-scripts'): void
  (e: 'insert-before', index: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { regenerateScript, addScript, updateScript, deleteScript } = useApi()

const currentText = ref(props.script.text)
const isSaving = ref(false)
const isDeleting = ref(false)
const isRegenerating = ref(false)
const textareaRef = ref()
const route = useRoute()
const projectId = ref(route.params.projectId)

// Focus new script item
onMounted(() => {
  if (props.script.isNew && textareaRef.value) {
    nextTick(() => {
      textareaRef.value.focus()
    })
  }
})

const handleSave = async () => {
  if (currentText.value.trim() === props.script.text && !props.script.isNew) {
    return // No change
  }
  
  if (currentText.value.trim() === '') {
    // If it's a new script and it's empty, just remove it from the list
    if (props.script.isNew) {
      const currentScripts = emit('get-scripts')
      emit('update-scripts', currentScripts.filter(s => s.id !== props.script.id))
    }
    return
  }

  isSaving.value = true
  try {
    let updatedScript;
    if (props.script.isNew) {
        updatedScript = await addScript({
            projectId: projectId.value,
            text: currentText.value,
            order_index: props.script.order_index,
        });
        message.success('新脚本已添加');
        // 通知父组件重新获取脚本数据
        emit('refresh-scripts')
    } else {
        updatedScript = await updateScript(props.script.id, currentText.value);
        message.success('脚本已更新');
        // 通知父组件重新获取脚本数据
        emit('refresh-scripts')
    }
  } catch (error) {
    console.error("Failed to save script:", error)
    message.error('保存失败，请重试')
    currentText.value = props.script.text // Revert on failure
  } finally {
    isSaving.value = false
  }
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除脚本？',
    content: '此操作不可撤销。',
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      isDeleting.value = true
      try {
        await deleteScript(props.script.id)
        message.success('脚本已删除')
        // 通知父组件重新获取脚本数据
        emit('refresh-scripts')
      } catch (error) {
        console.error("Failed to delete script:", error)
        message.error('删除失败，请重试')
      } finally {
        isDeleting.value = false
      }
    },
  })
}

const handleRegenerate = () => {
  Modal.confirm({
    title: '确认重新生成脚本？',
    content: '当前脚本内容将被覆盖。',
    okText: '确认生成',
    cancelText: '取消',
    onOk: async () => {
      isRegenerating.value = true
      try {
        const regeneratedText = await regenerateScript(props.script.id)
        currentText.value = regeneratedText.text
        message.success('脚本已重新生成')
        // 通知父组件重新获取脚本数据
        emit('refresh-scripts')
      } catch (error) {
        console.error("Failed to regenerate script:", error)
        message.error('重新生成失败')
      } finally {
        isRegenerating.value = false
      }
    }
  })
}

const handleInsertBefore = () => {
  emit('insert-before', props.index)
}
</script> 