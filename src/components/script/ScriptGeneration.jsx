import { useState, useEffect, useCallback } from 'react';
import { App } from 'antd';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import { useApi } from '../../hooks/useApi.js';
import useInterval from '../../hooks/useInterval.js';
import Button from '../common/Button';
import ScriptList from './ScriptList';
import LoadingSpinner from '../common/LoadingSpinner';

const ScriptGeneration = () => {
  const { state: appState } = useApp();
  const { setCurrentTab } = useNavigation();
  const { getProject, getScripts } = useApi();
  const { message } = App.useApp();
  
  const [scripts, setScripts] = useState([]);
  const [projectStatus, setProjectStatus] = useState('not_started');
  const [isLoading, setIsLoading] = useState(true);
  const [pollingDelay, setPollingDelay] = useState(null);

  const fetchProjectStatus = useCallback(async () => {
    if (!appState.projectId) return;

    try {
      const project = await getProject(appState.projectId);
      const status = project.script_generation_status;
      setProjectStatus(status);

      if (status === 'completed') {
        setPollingDelay(null); // Stop polling
        const scriptsData = await getScripts(appState.projectId);
        setScripts(scriptsData || []);
        setIsLoading(false);
      } else if (status === 'failed') {
        setPollingDelay(null);
        setIsLoading(false);
        message.error('脚本生成失败，请返回上一步重试。');
      } else { // in_progress or not_started
        setPollingDelay(3000); // Start or continue polling
        setIsLoading(false); // We are not 'page loading' anymore, but 'generating'
      }
    } catch (error) {
      console.error("Failed to fetch project status:", error);
      setPollingDelay(null);
      setIsLoading(false);
      message.error("无法获取项目状态，请刷新页面。");
    }
  }, [appState.projectId, getProject, getScripts, message]);

  // Main effect to kick off loading/polling
  useEffect(() => {
    fetchProjectStatus();
  }, [fetchProjectStatus]);

  // Polling interval
  useInterval(fetchProjectStatus, pollingDelay);

  const handleAddScript = () => {
    const newOrderIndex = scripts.length > 0 
      ? Math.max(...scripts.map(s => s.order_index)) + 1 
      : 0;

    const newScript = {
      id: `local_${Date.now()}`,
      text: '',
      isNew: true,
      order_index: newOrderIndex,
    };
    setScripts(prev => [...prev, newScript]);
  };

  const handleConfirmScript = () => {
    if (scripts.length === 0) {
      message.error('请先生成或添加脚本内容');
      return;
    }
    // Just navigate, generation will happen in the next step
    setCurrentTab('storyboard');
  };

  const renderContent = () => {
    if (isLoading) {
      return <div className="flex justify-center p-8"><LoadingSpinner text="正在加载项目数据..." /></div>;
    }
    if (projectStatus === 'in_progress' || projectStatus === 'not_started') {
        return <div className="flex justify-center p-8"><LoadingSpinner text="AI脚本生成中，请稍候..." /></div>;
    }
     if (projectStatus === 'failed') {
      return <div className="text-center py-12 text-red-500">脚本生成失败，请返回上一步重试。</div>;
    }

    return (
        <>
            <div className="mb-8">
                <ScriptList scripts={scripts} setScripts={setScripts} />
            </div>
            <div className="flex justify-between items-center">
                <Button variant="secondary" onClick={handleAddScript} icon={<span className="material-icons text-lg">add</span>}>
                    添加新条目
                </Button>
                <Button variant="primary" onClick={handleConfirmScript} disabled={scripts.length === 0}>
                    进入镜头编辑
                </Button>
            </div>
        </>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          AI脚本生成
        </h2>
        <p className="text-gray-600">
          AI已为您生成以下脚本，您可以对每一条进行编辑或重新生成
        </p>
      </div>
      {renderContent()}
    </div>
  );
};

export default ScriptGeneration;
