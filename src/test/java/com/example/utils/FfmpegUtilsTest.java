package com.example.utils;

import net.bramp.ffmpeg.FFprobe;
import net.bramp.ffmpeg.probe.FFmpegProbeResult;
import net.bramp.ffmpeg.probe.FFmpegStream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * FfmpegUtils 的测试用例。
 *
 * <p><b>重要:</b> 要运行这些测试，您需要在 'src/test/resources/sample-media' 目录下准备以下媒体文件：
 * <ol>
 *     <li><b>sample-image.png</b> - 一个用于测试的图片文件。</li>
 *     <li><b>sample-video-no-audio.mp4</b> - 一个没有音轨的短视频。</li>
 *     <li><b>sample-audio.mp3</b> - 一个音频文件。</li>
 *     <li><b>sample-video-main.mp4</b> - 一个用于画中画背景的视频。</li>
 *     <li><b>sample-video-overlay.mp4</b> - 一个用于画中画叠加的视频。</li>
 *     <li><b>sample-video-1.mp4</b> - 用于拼接测试的第一个视频 (建议与 video-2 编解码参数相同)。</li>
 *     <li><b>sample-video-2.mp4</b> - 用于拼接测试的第二个视频 (建议与 video-1 编解码参数相同)。</li>
 * </ol>
 *
 * <p>由于测试依赖外部ffmpeg程序和真实的媒体文件，默认将测试类标记为 {@code @Disabled}。
 * <p>在您准备好上述文件并配置好ffmpeg环境后，请移除此类上的 {@code @Disabled} 注解来运行测试。
 */
class FfmpegUtilsTest {

    @TempDir
    static Path tempDir;

    private static final Path resourceDir = Paths.get("src/test/resources/sample-media").toAbsolutePath();

    @Test
    void testCreateImageToVideo() throws IOException {
        Path imagePath = Paths.get("/Users/<USER>/Downloads/4a5a5e544dfa44a1af5d61de3b5c1b9d.png");
        Path outputPath = tempDir.resolve("/Users/<USER>/Downloads/4a5a5e544dfa44a1af5d61de3b5c1b9d2.mp4");
        int duration = 5;

        assertTrue(Files.exists(imagePath), "测试图片 " + imagePath + " 不存在。");

        FfmpegUtils.createImageToVideo(imagePath.toString(), outputPath.toString(), duration);

        assertTrue(Files.exists(outputPath), "输出文件未创建。");
        assertTrue(Files.size(outputPath) > 0, "输出文件大小为0。");

        // 使用 ffprobe 验证视频时长
        FFprobe ffprobe = new FFprobe();
        FFmpegProbeResult probeResult = ffprobe.probe(outputPath.toString());
        assertEquals(duration, (int) Math.round(probeResult.getFormat().duration), "视频时长与预期不符。");
    }

    @Test
    void testMergeVideoAndAudio() throws IOException {
        Path videoPath = Path.of("/Users/<USER>/projcets/video/Wav2Lip/result.mp4");
        Path audioPath = Path.of("/Users/<USER>/video-project/static/zf_xiaoyi.wav");
        Path outputPath = Path.of("/Users/<USER>/Downloads/zf_xiaoyi2.mp4");

        assertTrue(Files.exists(videoPath), "测试视频 " + videoPath + " 不存在。");
        assertTrue(Files.exists(audioPath), "测试音频 " + audioPath + " 不存在。");

        FfmpegUtils.mergeVideoAndAudio(videoPath.toString(), audioPath.toString(), outputPath.toString());

        assertTrue(Files.exists(outputPath), "输出文件未创建。");
        assertTrue(Files.size(outputPath) > 0, "输出文件大小为0。");

        // 使用 ffprobe 验证流
        FFprobe ffprobe = new FFprobe();
        FFmpegProbeResult probeResult = ffprobe.probe(outputPath.toString());
        long videoStreams = probeResult.getStreams().stream().filter(s -> s.codec_type == FFmpegStream.CodecType.VIDEO).count();
        long audioStreams = probeResult.getStreams().stream().filter(s -> s.codec_type == FFmpegStream.CodecType.AUDIO).count();

        assertEquals(1, videoStreams, "合并后的文件应包含一个视频流。");
        assertEquals(1, audioStreams, "合并后的文件应包含一个音频流。");
    }

    @Test
    void testCreatePictureInPicture() throws IOException {
        Path mainVideoPath = Path.of("/Users/<USER>/Downloads/8762656-uhd_3840_2160_25fps.mp4");
        Path overlayVideoPath = Path.of("/Users/<USER>/Downloads/zf_xiaoyi2.mp4");
        Path outputPath = Path.of("/Users/<USER>/Downloads/pip-video.mp4");

        assertTrue(Files.exists(mainVideoPath), "主视频文件不存在。");
        assertTrue(Files.exists(overlayVideoPath), "叠加视频文件不存在。");

        FfmpegUtils.createPictureInPicture(mainVideoPath.toString(), overlayVideoPath.toString(), outputPath.toString(), FfmpegUtils.PipPosition.BOTTOM_RIGHT);

        assertTrue(Files.exists(outputPath), "输出文件未创建。");
        assertTrue(Files.size(outputPath) > 0, "输出文件大小为0。");

        // 验证流数量
        FFprobe ffprobe = new FFprobe();
        FFmpegProbeResult probeResult = ffprobe.probe(outputPath.toString());
        assertTrue(probeResult.getStreams().stream().anyMatch(s -> s.codec_type == FFmpegStream.CodecType.VIDEO), "画中画视频应包含视频流。");
    }

    @Test
    void testConcatenateVideos() throws IOException {
        Path video1Path = Path.of("/Users/<USER>/Downloads/4a5a5e544dfa44a1af5d61de3b5c1b9d2.mp4");
        Path video2Path = Path.of("/Users/<USER>/Downloads/4a5a5e544dfa44a1af5d61de3b5c1b9d1.mp4");
        Path outputPath = Path.of("/Users/<USER>/Downloads/concatenated-video.mp4");

        assertTrue(Files.exists(video1Path), "拼接视频1不存在。");
        assertTrue(Files.exists(video2Path), "拼接视频2不存在。");

        List<String> videoPaths = Arrays.asList(video1Path.toString(), video2Path.toString());
        FfmpegUtils.concatenateVideos(videoPaths, outputPath.toString());

        assertTrue(Files.exists(outputPath), "输出文件未创建。");
        assertTrue(Files.size(outputPath) > 0, "输出文件大小为0。");

        // 使用 ffprobe 验证时长
        FFprobe ffprobe = new FFprobe();
        FFmpegProbeResult probeResult1 = ffprobe.probe(video1Path.toString());
        FFmpegProbeResult probeResult2 = ffprobe.probe(video2Path.toString());
        FFmpegProbeResult probeResultOut = ffprobe.probe(outputPath.toString());

        double expectedDuration = probeResult1.getFormat().duration + probeResult2.getFormat().duration;
        // 拼接视频的时长可能因为编码细节有微小差异，设置一个容忍度
        assertEquals(expectedDuration, probeResultOut.getFormat().duration, 0.2, "拼接后视频时长不等于各部分之和。");
    }
}
