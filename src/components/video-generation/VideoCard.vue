<template>
  <div class="bg-white rounded-lg border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200">
    <!-- Mobile: Vertical Layout, Desktop: Horizontal Layout -->
    <div class="flex flex-col md:flex-row">
      <!-- Left: Shot Image and Video Area -->
      <div class="md:w-80 md:h-44 h-48 bg-gray-50 relative flex-shrink-0">
        <!-- Video Player if generated -->
        <video 
          v-if="videoSettings.isGenerated && videoSettings.videoUrl"
          :src="videoSettings.videoUrl"
          :poster="videoSettings.thumbnailUrl || shot.imageUrl"
          class="w-full h-full object-cover"
          controls
          muted
          preload="metadata"
        />
        
        <!-- Shot Image (fallback) -->
        <img
          v-else
          :src="shot.imageUrl"
          :alt="`镜头 ${index + 1}`"
          class="w-full h-full object-cover"
        />
        
        <!-- Shot Number -->
        <div class="absolute top-3 left-3 w-6 h-6 bg-black bg-opacity-70 text-white rounded-full flex items-center justify-center text-xs font-medium">
          {{ index + 1 }}
        </div>

        <!-- Generating Overlay -->
        <div v-if="isGenerating" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div class="text-center text-white">
            <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <span class="text-xs">生成中</span>
          </div>
        </div>

        <!-- Video Status Badge -->
        <div class="absolute top-3 right-3">
          <div v-if="videoSettings.isGenerated" class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
            已生成
          </div>
          <div v-else-if="isGenerating" class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
            生成中
          </div>
          <div v-else class="bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium">
            待生成
          </div>
        </div>
      </div>

      <!-- Right: Content -->
      <div class="flex-1 p-4 flex flex-col justify-between">
        <!-- Top Section -->
        <div>
          <!-- Script Text -->
          <p class="text-sm text-gray-700 leading-relaxed mb-4 line-clamp-2">
            {{ shot.text }}
          </p>

          <!-- Resolution Selection -->
          <div class="mb-4">
            <label class="block text-xs text-gray-500 mb-2">视频分辨率</label>
            <a-select
              :value="videoSettings.resolution || '1080p'"
              @change="handleResolutionSelect"
              class="w-full"
              placeholder="选择分辨率"
            >
              <a-select-option 
                v-for="option in resolutionOptions" 
                :key="option.value" 
                :value="option.value"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <span class="text-base mr-2">{{ option.icon }}</span>
                    <div>
                      <div class="text-sm font-medium">{{ `${option.label} (${option.description})` }}</div>
                    </div>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>
        </div>

        <!-- Bottom Actions -->
        <div class="flex gap-2">
          <a-button
            type="primary"
            @click="onGenerate"
            :disabled="isGenerating"
            class="flex-1"
          >
            {{ videoSettings.isGenerated ? '重新生成' : (isGenerating ? '生成中...' : '生成视频') }}
          </a-button>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

interface Shot {
  id: string
  projectId: string
  text: string
  orderIndex: number
  shotNumber: number
  imageUrl: string
  audioUrl?: string
  videoUrl?: string
  createdAt: string
  updatedAt: string
}

interface VideoSettings {
  style?: string
  quality?: string
  duration?: number
  resolution?: string
  isGenerated?: boolean
  videoUrl?: string | null
  thumbnailUrl?: string | null
}

interface Props {
  shot: Shot
  index: number
  videoSettings: VideoSettings
  isGenerating: boolean
  onStyleSelect: (style: string) => void
  onResolutionSelect: (resolution: string) => void
  onGenerate: () => void
  onCustomize: () => void
}

const props = defineProps<Props>()

// 分辨率选项列表
const resolutionOptions = [
  {
    value: '720p',
    icon: '📺',
    label: '720p (HD)',
    description: '1280×720 横屏'
  },
  {
    value: '1080p',
    icon: '🎬',
    label: '1080p (Full HD)',
    description: '1920×1080 横屏'
  },
  {
    value: '4k',
    icon: '🎯',
    label: '4K (Ultra HD)',
    description: '3840×2160 横屏'
  },
  {
    value: '9:16-720p',
    icon: '📱',
    label: '720p 竖屏',
    description: '720×1280 竖屏'
  },
  {
    value: '9:16-1080p',
    icon: '📲',
    label: '1080p 竖屏',
    description: '1080×1920 竖屏'
  },
  {
    value: '1:1-1080p',
    icon: '⬜',
    label: '1080p 方形',
    description: '1080×1080 方形'
  }
]

const handleResolutionSelect = (resolution: string) => {
  props.onResolutionSelect(resolution)
}

</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 