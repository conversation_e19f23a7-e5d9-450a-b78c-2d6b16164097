package com.example.repository;

import com.example.entity.ScriptShot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 脚本镜头Repository接口
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Repository
public interface ScriptShotRepository extends JpaRepository<ScriptShot, String>, JpaSpecificationExecutor<ScriptShot> {

    /**
     * 根据项目ID查询脚本镜头，按顺序排序
     */
    List<ScriptShot> findByProjectIdOrderByOrderIndexAsc(String projectId);

    /**
     * 根据项目ID删除脚本镜头
     */
    @Modifying
    @Transactional
    void deleteByProjectId(String projectId);

    /**
     * 根据项目ID和顺序索引查询脚本镜头
     */
    ScriptShot findByProjectIdAndOrderIndex(String projectId, Integer orderIndex);

    /**
     * 查询项目中最大的orderIndex
     */
    @Query("SELECT MAX(s.orderIndex) FROM ScriptShot s WHERE s.projectId = ?1")
    Integer findMaxOrderIndexByProjectId(String projectId);

    /**
     * 查询项目中最大的shotNumber
     */
    @Query("SELECT MAX(s.shotNumber) FROM ScriptShot s WHERE s.projectId = ?1")
    Integer findMaxShotNumberByProjectId(String projectId);
} 