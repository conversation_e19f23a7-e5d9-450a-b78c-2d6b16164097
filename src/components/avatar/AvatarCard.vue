<template>
  <div class="bg-white rounded-lg border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200">
    <!-- Mobile: Vertical Layout, Desktop: Horizontal Layout -->
    <div class="flex flex-col md:flex-row">
      <!-- Left: Video/Image Area -->
      <div class="md:w-80 md:h-44 h-48 bg-gray-900 relative flex-shrink-0 text-white">
        <!-- Generated Video -->
        <video
          v-if="shot.videoUrl && avatarSettings.avatarType !== 'none'"
          :src="shot.videoUrl"
          class="w-full h-full object-contain"
          controls
          playsinline
        ></video>
        
        <!-- Shot Image (fallback) -->
        <img
          v-else
          :src="shot.imageUrl"
          :alt="`镜头 ${index + 1}`"
          class="w-full h-full object-cover"
        />
        
        <!-- Shot Number -->
        <div class="absolute top-3 left-3 w-6 h-6 bg-black bg-opacity-70 text-white rounded-full flex items-center justify-center text-xs font-medium">
          {{ index + 1 }}
        </div>

        <!-- Configuring Overlay -->
        <div v-if="isConfiguring" class="absolute inset-0 bg-black bg-opacity-70 flex items-center justify-center">
          <div class="text-center">
            <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <span class="text-xs">生成中</span>
          </div>
        </div>

        <!-- Avatar Status Badge -->
        <div class="absolute top-3 right-3">
          <div v-if="avatarSettings.avatarType === 'none'" class="bg-gray-400 text-white px-2 py-1 rounded text-xs font-medium">
            不使用
          </div>
          <div v-else-if="shot.videoUrl" class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
            已生成
          </div>
          <div v-else-if="isConfiguring" class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
            生成中
          </div>
          <div v-else class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-medium">
            待生成
          </div>
        </div>
      </div>

      <!-- Right: Content -->
      <div class="flex-1 p-4 flex flex-col justify-between">
        <!-- Top Section -->
        <div>
          <!-- Script Text -->
          <p class="text-sm text-gray-700 leading-relaxed mb-4 line-clamp-2">
            {{ shot.scriptText }}
          </p>

          <!-- Avatar Selection -->
          <div class="mb-4">
            <label class="block text-xs text-gray-500 mb-2">数字人</label>
            <a-select
              :value="avatarSettings.avatarType || 'none'"
              @change="handleAvatarSelect"
              class="w-full"
              :disabled="isConfiguring"
              placeholder="选择数字人或选择'不使用'"
            >
              <a-select-option value="none">
                <div class="flex items-center">
                  <span class="text-base mr-2 flex items-center">🚫</span>
                  <div class="text-sm font-medium text-center">不使用数字人</div>
                </div>
              </a-select-option>
              <a-select-option 
                v-for="avatar in avatarOptions" 
                :key="avatar.value" 
                :value="avatar.value"
              >
                <div class="flex items-center">
                  <span class="text-base mr-2 flex items-center">{{ avatar.icon }}</span>
                  <div class="text-sm font-medium text-center">{{ `${avatar.label}` }}</div>
                </div>
              </a-select-option>
            </a-select>
          </div>
        </div>

        <!-- Bottom Actions -->
        <div class="flex gap-2">
          <a-button
            type="primary"
            @click="onConfigure"
            :disabled="isConfiguring || avatarSettings.avatarType === 'none'"
            class="flex-1"
          >
            {{ shot.videoUrl ? '重新生成' : (isConfiguring ? '生成中...' : '生成视频') }}
          </a-button>
          <a-button
            @click="onCustomize"
            class="px-3 flex items-center justify-center"
            :disabled="isConfiguring || avatarSettings.avatarType === 'none'"
          >
            <SettingOutlined />
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SettingOutlined } from '@ant-design/icons-vue'

interface Shot {
  id: string
  scriptText: string
  imageUrl: string
  videoUrl?: string
  avatarId?: string
}

interface AvatarSettings {
  avatarType?: string
  isConfigured?: boolean
  previewUrl?: string | null
}

interface Props {
  shot: Shot
  index: number
  avatarSettings: AvatarSettings
  isConfiguring: boolean
  onAvatarSelect: (avatarType: string) => void
  onConfigure: () => void
  onCustomize: () => void
  avatarOptions: any[]
}

const props = defineProps<Props>()

const handleAvatarSelect = (avatarType: string) => {
  props.onAvatarSelect(avatarType)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 