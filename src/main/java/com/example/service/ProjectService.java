package com.example.service;

import com.example.common.exception.BusinessException;
import com.example.common.jpa.JpaSpecifications;
import com.example.common.response.ResultCode;
import com.example.entity.Asset;
import com.example.entity.Project;
import com.example.param.asset.AssetRecordVO;
import com.example.param.project.ProjectCreateParam;
import com.example.param.project.ProjectRecordVO;
import com.example.param.project.ProjectUpdateParam;
import com.example.repository.AssetRepository;
import com.example.repository.ProjectRepository;
import com.example.repository.ScriptShotRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 项目服务类
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectService {

    @Value("${app.upload-path}")
    private String uploadPath;

    @Value("${app.api-base-url}")
    private String apiBaseUrl;

    private final ProjectRepository projectRepository;
    private final AssetRepository assetRepository;
    private final ScriptShotRepository scriptShotRepository;

    /**
     * 创建项目
     */
    @Transactional
    public ProjectRecordVO createProject(ProjectCreateParam param) {
        log.info("创建项目: {}", param.getName());
        
        Project project = new Project();
        project.setId("proj_" + UUID.randomUUID().toString().replace("-", ""));
        project.setName(param.getName());
        project.setTheme(param.getTheme());
        project.setRequirements(param.getRequirements());
        project.setTextMaterial(param.getTextMaterial());
        project.setIsDraft(param.getIsDraft());
        project.setAvatarId(param.getAvatarId());
        project.setVoiceId(param.getVoiceId());
        project.setCreatedAt(LocalDateTime.now());
        project.setUpdatedAt(LocalDateTime.now());
        
        project = projectRepository.save(project);
        
        log.info("项目创建成功: {} - {}", project.getId(), project.getName());
        return ProjectRecordVO.from(project, List.of());
    }

    /**
     * 获取所有项目
     */
    public List<ProjectRecordVO> getAllProjects() {
        log.debug("获取所有项目");
        
        List<Project> projects = projectRepository.findAllByOrderByCreatedAtDesc();
        return projects.stream()
                .map(this::convertToProjectRecordVO)
                .toList();
    }

    /**
     * 根据ID获取项目
     */
    public ProjectRecordVO getProjectById(String projectId) {
        log.debug("获取项目详情: {}", projectId);
        
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));
        
        return convertToProjectRecordVO(project);
    }

    /**
     * 更新项目
     */
    @Transactional
    public ProjectRecordVO updateProject(String projectId, ProjectUpdateParam param) {
        log.info("更新项目: {}", projectId);
        
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));
        
        if (StringUtils.hasText(param.getName())) {
            project.setName(param.getName());
        }
        if (StringUtils.hasText(param.getTheme())) {
            project.setTheme(param.getTheme());
        }
        if (param.getRequirements() != null) {
            project.setRequirements(param.getRequirements());
        }
        if (param.getTextMaterial() != null) {
            project.setTextMaterial(param.getTextMaterial());
        }
        if (StringUtils.hasText(param.getAvatarId())) {
            project.setAvatarId(param.getAvatarId());
        }
        if (StringUtils.hasText(param.getVoiceId())) {
            project.setVoiceId(param.getVoiceId());
        }
        
        project = projectRepository.save(project);
        
        log.info("项目更新成功: {}", projectId);
        return convertToProjectRecordVO(project);
    }

    /**
     * 删除项目
     */
    @Transactional
    public void deleteProject(String projectId) {
        log.info("删除项目: {}", projectId);
        
        if (!projectRepository.existsById(projectId)) {
            throw new BusinessException(ResultCode.NOT_FOUND, "项目不存在");
        }
        
        // 删除关联的资产
        assetRepository.deleteByProjectId(projectId);

        // 删除关联的脚本镜头
        scriptShotRepository.deleteByProjectId(projectId);

        // 删除项目
        projectRepository.deleteById(projectId);
        
        log.info("项目删除成功: {}", projectId);
    }

    /**
     * 根据条件查询项目
     */
    public List<ProjectRecordVO> findProjectsByConditions(String name, Boolean isDraft) {
        log.debug("根据条件查询项目: name={}, isDraft={}", name, isDraft);
        
        Specification<Project> spec = JpaSpecifications.<Project>and()
                .like(StringUtils.hasText(name), Project::getName, "%" + name + "%")
                .eq(isDraft != null, Project::getIsDraft, isDraft)
                .build();
        
        List<Project> projects = projectRepository.findAll(spec);
        return projects.stream()
                .map(this::convertToProjectRecordVO)
                .toList();
    }

    /**
     * 转换为ProjectRecordVO
     */
    private ProjectRecordVO convertToProjectRecordVO(Project project) {
        List<AssetRecordVO> assets = assetRepository.findByProjectIdOrderByUploadedAtDesc(project.getId())
                .stream()
                .map(AssetRecordVO::from)
                .toList();
        
        return ProjectRecordVO.from(project, assets);
    }

    @Transactional
    public AssetRecordVO uploadAsset(String projectId, MultipartFile file) {
        long size = file.getSize();
        // 1. 校验项目是否存在
        Project project = projectRepository.findById(projectId)
            .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));

        // 2. 保存文件到本地（或云存储）
        String uploadDir = String.join(File.separator, uploadPath, projectId);

        File dir = new File(uploadDir);
        if (!dir.exists()) dir.mkdirs();
        String fileName = UUID.randomUUID() + "_" + file.getOriginalFilename();
        File dest = new File(dir, fileName);
        try {
            file.transferTo(dest);
        } catch (IOException e) {
            log.error("上传文件失败: {}", e.getMessage());
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "上传文件失败");
        }
        String fileUrl = String.join(File.separator, apiBaseUrl, "uploads", projectId, fileName);

        // 3. 保存文件信息到数据库
        Asset asset = new Asset();
        asset.setId("asset_" + UUID.randomUUID().toString().replace("-", ""));
        asset.setProjectId(projectId);
        asset.setOriginalName(file.getOriginalFilename());
        asset.setFilename(file.getOriginalFilename());
        asset.setPath(dest.getAbsolutePath());
        asset.setSize(size);
        asset.setUrl(fileUrl);
        asset.setUploadedAt(LocalDateTime.now());
        assetRepository.save(asset);

        // 4. 返回VO
        return AssetRecordVO.from(asset);
    }

    public void deleteAsset(String assetId) {
        log.info("删除项目素材: assetId={}", assetId);
        assetRepository.deleteById(assetId);
    }

    public List<AssetRecordVO> getProjectAssets(String projectId) {
        log.info("获取项目素材: projectId={}", projectId);
        List<Asset> assets = assetRepository.findByProjectIdOrderByUploadedAtDesc(projectId);
        return assets.stream()
                .map(AssetRecordVO::from)
                .toList();
    }

    @Async
    @Transactional
    public void composeProject(String projectId) {
        log.info("开始合成项目视频: {}", projectId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "项目不存在"));

        // 检查并更新合成状态
        if (project.getCompositionStatus() == Project.GenerationStatus.in_progress) {
            log.warn("项目 {} 正在合成中，请勿重复请求", projectId);
            return;
        }
        project.setCompositionStatus(Project.GenerationStatus.in_progress);
        projectRepository.save(project);

        // 模拟合成过程
        try {
            Thread.sleep(5000); // 模拟耗时

            String finalVideoUrl = "http://127.0.0.1:9800/static/final_video_output.mp4";
            project.setFinalVideoUrl(finalVideoUrl);
            project.setCompositionStatus(Project.GenerationStatus.completed);
            log.info("项目 {} 视频合成成功", projectId);
        } catch (InterruptedException e) {
            log.error("项目 {} 视频合成被中断", projectId, e);
            project.setCompositionStatus(Project.GenerationStatus.failed);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("项目 {} 视频合成失败", projectId, e);
            project.setCompositionStatus(Project.GenerationStatus.failed);
        } finally {
            projectRepository.save(project);
        }
    }
}