import { createRouter, createWebHistory } from 'vue-router'
import ProjectSelector from '../views/ProjectSelector.vue'
import ProjectDetail from '../views/ProjectDetail.vue'

const routes = [
  {
    path: '/',
    name: 'ProjectSelector',
    component: ProjectSelector
  },
  {
    path: '/project/:projectId',
    component: ProjectDetail,
    props: true,
    children: [
      {
        path: '',
        redirect: 'upload'
      },
      {
        path: 'upload',
        name: 'Upload',
        component: ProjectDetail,
        props: true,
        meta: { tab: 'upload' }
      },
      {
        path: 'script',
        name: 'Script',
        component: ProjectDetail,
        props: true,
        meta: { tab: 'script' }
      },
      {
        path: 'storyboard',
        name: 'Storyboard',
        component: ProjectDetail,
        props: true,
        meta: { tab: 'storyboard' }
      },
      {
        path: 'voice',
        name: 'Voice',
        component: ProjectDetail,
        props: true,
        meta: { tab: 'voice' }
      },
      {
        path: 'video',
        name: 'Video',
        component: ProjectDetail,
        props: true,
        meta: { tab: 'video' }
      },
      {
        path: 'avatar',
        name: 'Avatar',
        component: ProjectDetail,
        props: true,
        meta: { tab: 'avatar' }
      },
      {
        path: 'compose',
        name: 'Compose',
        component: ProjectDetail,
        props: true,
        meta: { tab: 'compose' }
      },
      {
        path: 'export',
        name: 'Export',
        component: ProjectDetail,
        props: true,
        meta: { tab: 'export' }
      }
    ]
  },
  // 向后兼容：旧的URL格式重定向到新格式
  {
    path: '/project/:projectId/legacy',
    redirect: (to: any) => {
      const tab = to.query.tab as string || 'upload'
      return `/project/${to.params.projectId}/${tab}`
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫：处理向后兼容的URL格式
router.beforeEach((to, from, next) => {
  // 如果访问的是旧格式的URL（有tab查询参数但不是新的路由结构）
  if (to.query.tab && to.path.match(/^\/project\/[^/]+$/) && !to.meta?.tab) {
    const tab = to.query.tab as string
    const projectId = to.params.projectId as string
    
    // 重定向到新的路由格式
    next(`/project/${projectId}/${tab}`)
    return
  }
  
  next()
})

export default router 