import { useEffect, useState } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { useApp } from '../../context/AppContext.jsx';
import { useApi } from '../../hooks/useApi.js';
import { useToast } from '../../hooks/useToast.js';
import MainLayout from '../layout/MainLayout';
import LoadingSpinner from '../common/LoadingSpinner';

const ProjectDetail = () => {
  const { projectId } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { dispatch, ActionTypes } = useApp();
  const { checkApiHealth, getProject } = useApi(); // getProject is used to check existence
  const { showError } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isProjectValid, setIsProjectValid] = useState(false);

  useEffect(() => {
    const validateProject = async () => {
      if (!projectId) {
        navigate('/');
        return;
      }

      setIsLoading(true);
      try {
        await checkApiHealth();
        
        // The primary goal now is just to validate the project exists
        // and set the projectId for other components to use.
        await getProject(projectId); 

        dispatch({ type: ActionTypes.SET_PROJECT_ID, payload: projectId });
        
        // Set the active tab from URL or default
        const tab = searchParams.get('tab');
        const targetTab = tab || 'upload'; 
        dispatch({ type: ActionTypes.SET_CURRENT_TAB, payload: targetTab });

        if (!tab) {
          const newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.set('tab', 'upload');
          navigate(`/project/${projectId}?${newSearchParams.toString()}`, { replace: true });
        }
        
        setIsProjectValid(true);

      } catch (error) {
        console.error('Failed to validate project:', error);
        if (error.message.includes('404') || error.message.includes('not found')) {
            setIsProjectValid(false);
        } else {
          showError('加载项目失败，请检查网络连接并重试');
        }
      } finally {
        setIsLoading(false);
      }
    };

    validateProject();
    // Clean up project-specific data when component unmounts or projectId changes
    return () => {
        dispatch({ type: ActionTypes.CLEAR_PROJECT_DATA });
    };

  }, [projectId, searchParams, dispatch, navigate, showError, ActionTypes, checkApiHealth, getProject]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" text="正在加载项目..." />
      </div>
    );
  }

  if (!isProjectValid) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="material-icons text-2xl text-red-600">error</span>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">项目不存在</h2>
          <p className="text-gray-600 mb-6">
            您访问的项目不存在或已被删除。
          </p>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  return <MainLayout />;
};

export default ProjectDetail;
