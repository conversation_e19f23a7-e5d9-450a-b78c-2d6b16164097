<template>
  <a-button
    :type="antdType"
    :size="size"
    :disabled="disabled"
    :html-type="type"
    :class="className"
    @click="handleClick"
  >
    <slot></slot>
  </a-button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'danger' | 'outline'
  size?: 'small' | 'middle' | 'large'
  disabled?: boolean
  type?: 'button' | 'submit' | 'reset'
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'middle',
  disabled: false,
  type: 'button',
  className: ''
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 将自定义 variant 转换为 Ant Design 的 type
const antdType = computed(() => {
  const variantMap = {
    primary: 'primary',
    secondary: 'default',
    danger: 'primary',
    outline: 'default'
  }
  return variantMap[props.variant]
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>

<style scoped>
/* 为 danger 变体添加自定义样式 */
:deep(.ant-btn-primary.danger) {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
}

:deep(.ant-btn-primary.danger:hover) {
  background-color: #ff7875;
  border-color: #ff7875;
}
</style> 