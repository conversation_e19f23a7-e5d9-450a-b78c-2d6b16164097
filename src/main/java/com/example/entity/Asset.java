package com.example.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 资产实体
 *
 * <AUTHOR> Backend Generator
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "t_asset")
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class Asset {

    @Id
    @Column(length = 50)
    private String id;

    @Column(name = "project_id", nullable = false, length = 50)
    private String projectId;

    @Column(name = "original_name", nullable = false, length = 255)
    private String originalName;

    @Column(nullable = false, length = 255)
    private String filename;

    private String path;

    private Long size;

    @Column(length = 500)
    private String url;

    @CreatedDate
    @Column(name = "uploaded_at", nullable = false, updatable = false)
    private LocalDateTime uploadedAt;

    // 关联项目
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    private Project project;
} 