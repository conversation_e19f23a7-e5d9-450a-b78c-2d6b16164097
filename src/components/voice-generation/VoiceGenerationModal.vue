<template>
  <a-modal
    :open="isOpen"
    title="自定义配音设置"
    :width="600"
    class="voice-generation-modal"
    @ok="handleGenerate"
    @cancel="handleCancel"
    ok-text="生成配音"
    cancel-text="取消"
  >
    <div class="space-y-4 max-h-[60vh] overflow-y-auto">
      <!-- Shot Preview -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          当前镜头
        </label>
        <div class="flex gap-3 p-3 bg-gray-50 rounded-lg">
          <img
            :src="shotData?.shot?.imageUrl"
            alt="镜头预览"
            class="w-[192px] h-[108px] object-cover rounded flex-shrink-0"
          />
          <div class="flex-1 min-w-0">
            <p class="text-sm text-gray-700 leading-relaxed line-clamp-2">
              {{ shotData?.shot?.text }}
            </p>
          </div>
        </div>
      </div>

      <!-- Voice Settings - Compact Grid Layout -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div v-for="[key, values] in Object.entries(options)" :key="key">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ getSettingLabel(key) }}
          </label>
          <div class="space-y-1">
            <label
              v-for="option in values"
              :key="option.value"
                             :class="[
                 'flex items-center p-2 border rounded cursor-pointer transition-all duration-200',
                 (settings as any)[key] === option.value
                   ? 'border-primary bg-blue-50'
                   : 'border-gray-200 hover:border-gray-300'
               ]"
            >
              <input
                type="radio"
                :name="key"
                :value="option.value"
                                 :checked="(settings as any)[key] === option.value"
                @change="() => handleSettingChange(key, option.value)"
                class="w-4 h-4 text-primary focus:ring-primary border-gray-300 mr-2"
              />
              <div class="flex-1 min-w-0 ml-2">
                <div class="text-sm font-medium text-gray-900">
                  {{ option.label }}
                </div>
                <div class="text-xs text-gray-500 truncate">
                  {{ option.description }}
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Custom Prompt -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          自定义要求 (可选)
        </label>
        <a-textarea
          v-model:value="settings.customPrompt"
          placeholder="例如：在某些词语上加重语气、增加停顿、调整语调变化等..."
          :rows="2"
          class="resize-none"
        />
        <p class="text-xs text-gray-500 mt-1">
          描述您对这段配音的特殊要求
        </p>
      </div>

      <!-- Preview Settings - Compact -->
      <div class="bg-gray-50 rounded-lg p-3">
        <h4 class="text-sm font-medium text-gray-700 mb-2">当前设置预览</h4>
        <div class="grid grid-cols-2 gap-2 text-xs">
          <div class="flex justify-between">
            <span class="text-gray-600">语速:</span>
            <span class="text-gray-900 font-medium">
              {{ getOptionLabel('speed', settings.speed) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">音调:</span>
            <span class="text-gray-900 font-medium">
              {{ getOptionLabel('pitch', settings.pitch) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">情感:</span>
            <span class="text-gray-900 font-medium">
              {{ getOptionLabel('emotion', settings.emotion) }}
            </span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">强调:</span>
            <span class="text-gray-900 font-medium">
              {{ getOptionLabel('emphasis', settings.emphasis) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Shot {
  id: string
  imageUrl: string
  text: string
  audioUrl?: string
}

interface ShotData {
  shotId: string
  shot: Shot
}

interface Props {
  isOpen: boolean
  onClose: () => void
  shotData?: ShotData | null
  onGenerate: (shotId: string) => void
}

const props = defineProps<Props>()

const settings = ref({
  speed: 'normal',
  pitch: 'normal',
  emotion: 'neutral',
  emphasis: 'none',
  customPrompt: ''
})

const options = {
  speed: [
    { value: 'slow', label: '慢速 (0.8x)', description: '适合教学内容' },
    { value: 'normal', label: '正常 (1.0x)', description: '标准语速' },
    { value: 'fast', label: '快速 (1.2x)', description: '适合快节奏内容' }
  ],
  pitch: [
    { value: 'low', label: '低音调', description: '更加沉稳' },
    { value: 'normal', label: '正常音调', description: '自然音调' },
    { value: 'high', label: '高音调', description: '更加活泼' }
  ],
  emotion: [
    { value: 'neutral', label: '中性', description: '平稳自然' },
    { value: 'happy', label: '愉悦', description: '积极向上' },
    { value: 'serious', label: '严肃', description: '正式庄重' },
    { value: 'gentle', label: '温和', description: '亲切友好' }
  ],
  emphasis: [
    { value: 'none', label: '无重点', description: '平均语调' },
    { value: 'keywords', label: '关键词', description: '突出重要词汇' },
    { value: 'sentences', label: '句子', description: '突出重要句子' }
  ]
}

const handleSettingChange = (key: string, value: string) => {
  settings.value = { ...settings.value, [key]: value } as typeof settings.value
}

const handleGenerate = () => {
  if (props.shotData?.shotId) {
    props.onGenerate(props.shotData.shotId)
  }
  props.onClose()
}

const handleCancel = () => {
  settings.value = {
    speed: 'normal',
    pitch: 'normal',
    emotion: 'neutral',
    emphasis: 'none',
    customPrompt: ''
  }
  props.onClose()
}

const getSettingLabel = (key: string) => {
  const labels = {
    speed: '语速设置',
    pitch: '音调设置',
    emotion: '情感色彩',
    emphasis: '重点强调'
  }
  return labels[key as keyof typeof labels] || key
}

const getOptionLabel = (category: string, value: string) => {
  const categoryOptions = options[category as keyof typeof options]
  const option = categoryOptions?.find(opt => opt.value === value)
  return option?.label || value
}

// 监听模态框关闭，重置设置
watch(() => props.isOpen, (newVal) => {
  if (!newVal) {
    settings.value = {
      speed: 'normal',
      pitch: 'normal',
      emotion: 'neutral',
      emphasis: 'none',
      customPrompt: ''
    }
  }
})
</script>

<style scoped>
.voice-generation-modal :deep(.ant-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.border-primary {
  border-color: #1890ff;
}

.text-primary {
  color: #1890ff;
}

.focus\:ring-primary:focus {
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}
</style> 